"""
🦗 Streamlit Tabanlı Ağustos Böceği Analiz Arayüzü
Streamlit-based Cicada Analysis Interface

Gradio i18n sorunlarını önlemek için Streamlit kullanımı
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Cicada analyzer import
try:
    from cicada_analyzer.core.cicada_filter import CicadaFilter
    
    @st.cache_resource
    def initialize_system():
        """Sistem başlatma (cache ile)"""
        st.write("🦗 Sistem başlatılıyor...")
        cicada_filter = CicadaFilter()
        st.write("✅ Sistem hazır!")
        return cicada_filter
    
except ImportError as e:
    st.error(f"❌ Import hatası: {e}")
    cicada_filter = None

def main():
    """Ana uygulama"""
    
    # Sayfa konfigürasyonu
    st.set_page_config(
        page_title="🦗 Cicada Analysis",
        page_icon="🦗",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Ana başlık
    st.title("🦗 Ağustos Böceği Sıcaklık Analizi")
    st.subheader("Dolbear Yasası ile Sıcaklık Tahmini")
    
    # Sidebar kontrolleri
    st.sidebar.header("🎛️ Analiz Ayarları")
    
    # Dosya yükleme
    uploaded_file = st.sidebar.file_uploader(
        "🎵 Audio Dosyası Seçin",
        type=['wav', 'mp3', 'm4a', 'flac'],
        help="Ağustos böceği sesi içeren audio dosyası yükleyin"
    )
    
    # Analiz seçenekleri
    enable_denoising = st.sidebar.checkbox(
        "🧹 Ses Temizleme (Denoising)", 
        value=True,
        help="Facebook Demucs ile ses temizleme"
    )
    
    enable_chirp = st.sidebar.checkbox(
        "🔍 Chirp Tespiti", 
        value=True,
        help="Gelişmiş chirp tespit algoritması"
    )
    
    temp_method = st.sidebar.selectbox(
        "🌡️ Sıcaklık Hesaplama Yöntemi",
        options=[
            ("basic_celsius", "Temel Dolbear (Celsius)"),
            ("cicada_orni_special", "Cicada orni Özel"),
            ("dolbear_fahrenheit", "Dolbear (Fahrenheit)")
        ],
        format_func=lambda x: x[1],
        help="Sıcaklık hesaplama algoritması seçin"
    )
    
    # Ana içerik alanı
    col1, col2 = st.columns([2, 1])
    
    with col1:
        if uploaded_file is not None:
            st.success(f"✅ Dosya yüklendi: {uploaded_file.name}")
            
            # Analiz butonu
            if st.button("🚀 Analiz Et", type="primary", use_container_width=True):
                
                # Progress bar
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                try:
                    # Sistem başlat
                    status_text.text("🔧 Sistem başlatılıyor...")
                    progress_bar.progress(20)
                    
                    cicada_filter = initialize_system()
                    if cicada_filter is None:
                        st.error("❌ Sistem başlatılamadı")
                        return
                    
                    # Geçici dosya kaydet
                    status_text.text("💾 Dosya işleniyor...")
                    progress_bar.progress(40)
                    
                    import tempfile
                    import os
                    
                    with tempfile.NamedTemporaryFile(delete=False, suffix=f".{uploaded_file.name.split('.')[-1]}") as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        temp_path = tmp_file.name
                    
                    # Analiz yap
                    status_text.text("🔍 Audio analiz ediliyor...")
                    progress_bar.progress(60)
                    
                    result = cicada_filter.process_audio_file(
                        file_path=temp_path,
                        enable_denoising=enable_denoising,
                        enable_chirp_detection=enable_chirp,
                        temperature_method=temp_method[0]
                    )
                    
                    progress_bar.progress(80)
                    
                    # Geçici dosyayı sil
                    try:
                        os.unlink(temp_path)
                    except:
                        pass
                    
                    if result['success']:
                        status_text.text("✅ Analiz tamamlandı!")
                        progress_bar.progress(100)
                        
                        temp_info = result['temperature_info']
                        
                        # Sonuçları göster
                        st.success("🎉 Analiz başarıyla tamamlandı!")
                        
                        # Metrik kartları
                        col_a, col_b, col_c, col_d = st.columns(4)
                        
                        with col_a:
                            st.metric(
                                "🌡️ Sıcaklık",
                                f"{temp_info['estimated_temperature']:.1f}°C",
                                help="Dolbear yasasına göre tahmin edilen sıcaklık"
                            )
                        
                        with col_b:
                            st.metric(
                                "🔊 Chirp Sayısı",
                                temp_info['chirp_count'],
                                help="Tespit edilen toplam chirp sayısı"
                            )
                        
                        with col_c:
                            st.metric(
                                "📊 Ortalama Frekans",
                                f"{temp_info['avg_chirp_frequency']:.2f} Hz",
                                help="Ortalama chirp frekansı"
                            )
                        
                        with col_d:
                            st.metric(
                                "✅ Güven Seviyesi",
                                f"{temp_info['confidence_level']:.1f}%",
                                help="Analiz güven seviyesi"
                            )
                        
                        # Görselleştirme
                        st.subheader("📈 Görselleştirme")
                        
                        fig, ax = plt.subplots(figsize=(10, 6))
                        ax.text(0.5, 0.5, f"Sıcaklık: {temp_info['estimated_temperature']:.1f}°C", 
                               ha='center', va='center', fontsize=24, 
                               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
                        ax.set_xlim(0, 1)
                        ax.set_ylim(0, 1)
                        ax.axis('off')
                        ax.set_title("🦗 Ağustos Böceği Sıcaklık Analizi", fontsize=16)
                        
                        st.pyplot(fig)
                        
                        # Detaylı sonuçlar
                        with st.expander("📋 Detaylı Sonuçlar"):
                            st.json({
                                "estimated_temperature": temp_info['estimated_temperature'],
                                "chirp_count": temp_info['chirp_count'],
                                "avg_chirp_frequency": temp_info['avg_chirp_frequency'],
                                "confidence_level": temp_info['confidence_level'],
                                "method_used": temp_method[0]
                            })
                        
                        # Progress bar'ı temizle
                        progress_bar.empty()
                        status_text.empty()
                        
                    else:
                        st.error(f"❌ Analiz hatası: {result.get('error', 'Bilinmeyen hata')}")
                        progress_bar.empty()
                        status_text.empty()
                        
                except Exception as e:
                    st.error(f"❌ Sistem hatası: {str(e)}")
                    progress_bar.empty()
                    status_text.empty()
        else:
            st.info("👆 Lütfen sidebar'dan bir audio dosyası yükleyin")
    
    with col2:
        # Bilgi paneli
        st.subheader("📚 Dolbear Yasası")
        st.markdown("""
        **Amos Dolbear (1897)** tarafından keşfedilen bu yasa:
        
        **T(°C) = N₈ + 5**
        
        - N₈: 8 saniyedeki chirp sayısı
        - T: Sıcaklık (Celsius)
        
        **Bilimsel Temeli:**
        - Ağustos böcekleri soğukkanlı (ectothermic)
        - Metabolizmaları sıcaklığa bağlı
        - Yüksek sıcaklıkta daha hızlı chirp
        """)
        
        # Sistem durumu
        st.subheader("🔧 Sistem Durumu")
        if st.button("🔄 Sistem Kontrolü"):
            try:
                cicada_filter = initialize_system()
                if cicada_filter:
                    st.success("✅ Sistem hazır")
                else:
                    st.error("❌ Sistem hatası")
            except Exception as e:
                st.error(f"❌ Hata: {str(e)}")

if __name__ == "__main__":
    main()
