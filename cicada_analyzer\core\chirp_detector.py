"""
Chirp Detector - Chirp tespiti ve sayma / Chirp detection and counting

Bu modül ağustos böceği seslerindeki chirp'leri tespit eder ve sayar.
Dolbear yasası için gerekli chirp oranlarını hesaplar.

Author: <PERSON><PERSON>lu
"""

import numpy as np
import librosa
from scipy.signal import find_peaks, butter, filtfilt
from scipy.ndimage import gaussian_filter1d
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from ..utils.audio_utils import AudioUtils

class ChirpDetector:
    """Ağustos böceği chirp tespiti sınıfı"""
    
    # Chirp tespit parametreleri
    CHIRP_CONFIG = {
        'min_chirp_duration': 0.05,    # Minimum chirp süresi (saniye)
        'max_chirp_duration': 1.0,     # Maksimum chirp süresi (saniye)
        'min_chirp_interval': 0.02,    # Minimum chirp arası süre
        'energy_threshold': 0.1,       # Enerji eş<PERSON>ği
        'prominence_threshold': 0.05,   # Peak prominence eşiği
        'frequency_range': (2000, 12000),  # Ağustos bö<PERSON>ği frekans aralığı
        'envelope_smoothing': 2.0,     # Envelope smoothing sigma
        'adaptive_threshold': True      # Adaptif eşik kullan
    }
    
    def __init__(self, sr: int = 22050):
        """
        Chirp Detector'ı başlat
        
        Args:
            sr: Sample rate
        """
        self.sr = sr
        self.config = self.CHIRP_CONFIG.copy()
        
        print(f"🔍 Chirp Detector başlatıldı / initialized (SR: {sr}Hz)")
    
    def detect_chirps(self, audio: np.ndarray, 
                     apply_filter: bool = True,
                     adaptive_threshold: bool = None) -> Dict:
        """
        Audio sinyalindeki chirp'leri tespit et
        
        Args:
            audio: Audio sinyal
            apply_filter: Band-pass filter uygula
            adaptive_threshold: Adaptif eşik kullan
            
        Returns:
            Chirp tespit sonuçları
        """
        if len(audio) == 0:
            return self._create_empty_result("Boş audio sinyali / Empty audio signal")
        
        adaptive_threshold = adaptive_threshold if adaptive_threshold is not None else self.config['adaptive_threshold']
        
        try:
            # Preprocessing
            processed_audio = self._preprocess_for_chirp_detection(audio, apply_filter)
            
            # Envelope çıkarma
            envelope = self._extract_envelope(processed_audio)
            
            # Threshold belirleme
            threshold = self._calculate_threshold(envelope, adaptive_threshold)
            
            # Peak detection
            peaks, peak_properties = self._detect_peaks(envelope, threshold)
            
            # Chirp validasyonu
            valid_chirps, chirp_details = self._validate_chirps(peaks, envelope, processed_audio)
            
            # Chirp karakteristikleri
            chirp_characteristics = self._analyze_chirp_characteristics(
                processed_audio, valid_chirps, envelope
            )
            
            # Temporal analiz
            temporal_analysis = self._perform_temporal_analysis(valid_chirps, len(audio))
            
            # Window bazında analiz
            window_analysis = self._analyze_chirps_by_windows(valid_chirps, len(audio))
            
            # Sonuçları birleştir
            result = {
                'success': True,
                'total_chirps': len(valid_chirps),
                'chirp_rate': len(valid_chirps) / (len(audio) / self.sr),
                'chirp_positions': valid_chirps.tolist(),
                'chirp_times': (valid_chirps / self.sr).tolist(),
                'duration': len(audio) / self.sr,
                'sample_rate': self.sr,
                'filter_applied': apply_filter,
                'threshold_used': threshold,
                'adaptive_threshold': adaptive_threshold,
                'chirp_details': chirp_details,
                'characteristics': chirp_characteristics,
                'temporal_analysis': temporal_analysis,
                'window_analysis': window_analysis,
                'envelope_length': len(envelope),
                'peak_properties': {
                    'heights': peak_properties.get('peak_heights', []).tolist(),
                    'prominences': peak_properties.get('prominences', []).tolist(),
                    'widths': peak_properties.get('widths', []).tolist()
                }
            }
            
            print(f"✅ Chirp tespiti tamamlandı / Chirp detection completed")
            print(f"   Toplam chirp / Total chirps: {len(valid_chirps)}")
            print(f"   Chirp oranı / Chirp rate: {result['chirp_rate']:.2f} chirp/s")
            
            return result
            
        except Exception as e:
            error_msg = f"Chirp tespit hatası / Chirp detection error: {str(e)}"
            print(f"❌ {error_msg}")
            return self._create_empty_result(error_msg)
    
    def _preprocess_for_chirp_detection(self, audio: np.ndarray, apply_filter: bool) -> np.ndarray:
        """
        Chirp tespiti için ön işleme
        
        Args:
            audio: Audio sinyal
            apply_filter: Band-pass filter uygula
            
        Returns:
            Ön işlenmiş audio
        """
        processed = audio.copy()
        
        # Band-pass filter (ağustos böceği frekans aralığı)
        if apply_filter:
            processed = AudioUtils.apply_bandpass_filter(
                processed, self.sr,
                self.config['frequency_range'][0],
                self.config['frequency_range'][1]
            )
        
        # Normalizasyon
        processed = AudioUtils.normalize_audio(processed, method='peak')
        
        return processed
    
    def _extract_envelope(self, audio: np.ndarray) -> np.ndarray:
        """
        Audio sinyalinin envelope'ını çıkar
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Envelope sinyali
        """
        # Method 1: Hilbert transform
        try:
            analytic_signal = librosa.util.frame(audio, frame_length=1024, hop_length=256)
            envelope = np.mean(np.abs(analytic_signal), axis=0)
        except:
            # Fallback: RMS energy
            hop_length = 256
            envelope = librosa.feature.rms(y=audio, hop_length=hop_length)[0]
        
        # Smoothing
        if self.config['envelope_smoothing'] > 0:
            envelope = gaussian_filter1d(envelope, sigma=self.config['envelope_smoothing'])
        
        return envelope
    
    def _calculate_threshold(self, envelope: np.ndarray, adaptive: bool) -> float:
        """
        Chirp tespiti için eşik değeri hesapla
        
        Args:
            envelope: Envelope sinyali
            adaptive: Adaptif eşik kullan
            
        Returns:
            Threshold değeri
        """
        if adaptive:
            # Adaptif eşik: ortanca + standart sapma
            median_energy = np.median(envelope)
            std_energy = np.std(envelope)
            threshold = median_energy + 2 * std_energy
        else:
            # Sabit eşik
            threshold = self.config['energy_threshold']
        
        # Minimum eşik kontrolü
        min_threshold = np.max(envelope) * 0.1
        threshold = max(threshold, min_threshold)
        
        return threshold
    
    def _detect_peaks(self, envelope: np.ndarray, threshold: float) -> Tuple[np.ndarray, Dict]:
        """
        Envelope'da peak'leri tespit et
        
        Args:
            envelope: Envelope sinyali
            threshold: Threshold değeri
            
        Returns:
            Tuple[peak_positions, peak_properties]
        """
        # Peak detection parametreleri
        min_distance = int(self.config['min_chirp_interval'] * self.sr / 256)  # Envelope hop_length=256
        
        peaks, properties = find_peaks(
            envelope,
            height=threshold,
            prominence=self.config['prominence_threshold'],
            distance=min_distance
        )
        
        return peaks, properties
    
    def _validate_chirps(self, peaks: np.ndarray, envelope: np.ndarray, 
                        audio: np.ndarray) -> Tuple[np.ndarray, List[Dict]]:
        """
        Tespit edilen peak'leri chirp olarak doğrula
        
        Args:
            peaks: Peak pozisyonları
            envelope: Envelope sinyali
            audio: Orijinal audio
            
        Returns:
            Tuple[valid_chirp_positions, chirp_details]
        """
        hop_length = len(audio) // len(envelope) if len(envelope) > 0 else 256
        valid_chirps = []
        chirp_details = []
        
        for i, peak in enumerate(peaks):
            # Peak'i audio pozisyonuna dönüştür
            audio_position = peak * hop_length
            
            # Chirp süresini hesapla
            duration = self._estimate_chirp_duration(peak, envelope, audio_position, audio)
            
            # Chirp frekansını analiz et
            frequency_analysis = self._analyze_chirp_frequency(audio_position, audio, duration)
            
            # Validasyon kriterleri
            is_valid = self._validate_single_chirp(duration, frequency_analysis)
            
            chirp_detail = {
                'peak_index': int(peak),
                'audio_position': int(audio_position),
                'time': float(audio_position / self.sr),
                'duration': duration,
                'amplitude': float(envelope[peak]),
                'frequency_analysis': frequency_analysis,
                'is_valid': is_valid
            }
            
            chirp_details.append(chirp_detail)
            
            if is_valid:
                valid_chirps.append(audio_position)
        
        return np.array(valid_chirps), chirp_details
    
    def _estimate_chirp_duration(self, peak: int, envelope: np.ndarray, 
                               audio_position: int, audio: np.ndarray) -> float:
        """
        Chirp süresini tahmin et
        
        Args:
            peak: Peak pozisyonu (envelope'da)
            envelope: Envelope sinyali
            audio_position: Audio pozisyonu
            audio: Audio sinyal
            
        Returns:
            Tahmin edilen chirp süresi
        """
        try:
            # Envelope'da threshold'un %50'sine düşene kadar olan süre
            peak_amplitude = envelope[peak]
            half_threshold = peak_amplitude * 0.5
            
            # Sol ve sağ sınırları bul
            left_bound = peak
            right_bound = peak
            
            # Sol taraf
            for i in range(peak, max(0, peak - 50), -1):
                if envelope[i] < half_threshold:
                    left_bound = i
                    break
            
            # Sağ taraf
            for i in range(peak, min(len(envelope), peak + 50)):
                if envelope[i] < half_threshold:
                    right_bound = i
                    break
            
            # Süreyi hesapla
            hop_length = len(audio) // len(envelope) if len(envelope) > 0 else 256
            duration_samples = (right_bound - left_bound) * hop_length
            duration = duration_samples / self.sr
            
            return max(0.01, min(duration, 2.0))  # 0.01-2.0 saniye arası
            
        except:
            return 0.1  # Varsayılan değer
    
    def _analyze_chirp_frequency(self, position: int, audio: np.ndarray, 
                               duration: float) -> Dict:
        """
        Chirp'in frekans özelliklerini analiz et
        
        Args:
            position: Chirp pozisyonu
            audio: Audio sinyal
            duration: Chirp süresi
            
        Returns:
            Frekans analizi sonuçları
        """
        try:
            # Chirp segmentini çıkar
            start = max(0, int(position - duration * self.sr / 2))
            end = min(len(audio), int(position + duration * self.sr / 2))
            chirp_segment = audio[start:end]
            
            if len(chirp_segment) == 0:
                return {'dominant_frequency': 0, 'spectral_centroid': 0, 'bandwidth': 0}
            
            # FFT analizi
            fft = np.fft.rfft(chirp_segment)
            freqs = np.fft.rfftfreq(len(chirp_segment), 1/self.sr)
            magnitude = np.abs(fft)
            
            # Ağustos böceği frekans aralığında filtrele
            freq_mask = (freqs >= self.config['frequency_range'][0]) & \
                       (freqs <= self.config['frequency_range'][1])
            
            if not np.any(freq_mask):
                return {'dominant_frequency': 0, 'spectral_centroid': 0, 'bandwidth': 0}
            
            filtered_freqs = freqs[freq_mask]
            filtered_magnitude = magnitude[freq_mask]
            
            # Dominant frekans
            dominant_freq = filtered_freqs[np.argmax(filtered_magnitude)]
            
            # Spectral centroid
            total_magnitude = np.sum(filtered_magnitude)
            if total_magnitude > 0:
                spectral_centroid = np.sum(filtered_freqs * filtered_magnitude) / total_magnitude
            else:
                spectral_centroid = 0
            
            # Bandwidth (80% enerji aralığı)
            cumulative_energy = np.cumsum(filtered_magnitude**2)
            total_energy = cumulative_energy[-1]
            
            if total_energy > 0:
                low_freq_idx = np.argmax(cumulative_energy >= 0.1 * total_energy)
                high_freq_idx = np.argmax(cumulative_energy >= 0.9 * total_energy)
                bandwidth = filtered_freqs[high_freq_idx] - filtered_freqs[low_freq_idx]
            else:
                bandwidth = 0
            
            return {
                'dominant_frequency': float(dominant_freq),
                'spectral_centroid': float(spectral_centroid),
                'bandwidth': float(bandwidth),
                'energy_in_range': float(total_magnitude)
            }
            
        except Exception as e:
            return {'dominant_frequency': 0, 'spectral_centroid': 0, 'bandwidth': 0, 'error': str(e)}
    
    def _validate_single_chirp(self, duration: float, frequency_analysis: Dict) -> bool:
        """
        Tek bir chirp'i doğrula
        
        Args:
            duration: Chirp süresi
            frequency_analysis: Frekans analizi
            
        Returns:
            Chirp geçerli mi?
        """
        # Süre kontrolü
        duration_valid = (self.config['min_chirp_duration'] <= duration <= 
                         self.config['max_chirp_duration'])
        
        # Frekans kontrolü
        dominant_freq = frequency_analysis.get('dominant_frequency', 0)
        frequency_valid = (self.config['frequency_range'][0] <= dominant_freq <= 
                          self.config['frequency_range'][1])
        
        # Enerji kontrolü
        energy_valid = frequency_analysis.get('energy_in_range', 0) > 0
        
        return duration_valid and frequency_valid and energy_valid
    
    def _analyze_chirp_characteristics(self, audio: np.ndarray, chirps: np.ndarray, 
                                     envelope: np.ndarray) -> Dict:
        """
        Chirp karakteristiklerini analiz et
        
        Args:
            audio: Audio sinyal
            chirps: Chirp pozisyonları
            envelope: Envelope sinyali
            
        Returns:
            Chirp karakteristikleri
        """
        if len(chirps) == 0:
            return {
                'mean_interval': 0,
                'std_interval': 0,
                'regularity_score': 0,
                'intensity_variation': 0
            }
        
        # Chirp aralıkları
        if len(chirps) > 1:
            intervals = np.diff(chirps) / self.sr  # Saniye cinsinden
            mean_interval = float(np.mean(intervals))
            std_interval = float(np.std(intervals))
            
            # Regularity score (düşük std = daha düzenli)
            regularity_score = 1.0 / (1.0 + std_interval) if std_interval > 0 else 1.0
        else:
            mean_interval = 0
            std_interval = 0
            regularity_score = 1.0
        
        # Intensity variation
        hop_length = len(audio) // len(envelope) if len(envelope) > 0 else 256
        chirp_intensities = []
        
        for chirp_pos in chirps:
            envelope_pos = int(chirp_pos // hop_length)
            if 0 <= envelope_pos < len(envelope):
                chirp_intensities.append(envelope[envelope_pos])
        
        if chirp_intensities:
            intensity_variation = float(np.std(chirp_intensities) / np.mean(chirp_intensities))
        else:
            intensity_variation = 0
        
        return {
            'mean_interval': mean_interval,
            'std_interval': std_interval,
            'regularity_score': regularity_score,
            'intensity_variation': intensity_variation,
            'total_chirps': len(chirps)
        }
    
    def _perform_temporal_analysis(self, chirps: np.ndarray, audio_length: int) -> Dict:
        """
        Temporal (zamansal) analiz
        
        Args:
            chirps: Chirp pozisyonları
            audio_length: Audio uzunluğu
            
        Returns:
            Temporal analiz sonuçları
        """
        if len(chirps) == 0:
            return {'density': 0, 'distribution': 'uniform', 'activity_periods': []}
        
        duration = audio_length / self.sr
        density = len(chirps) / duration
        
        # Chirp dağılımını analiz et
        if len(chirps) > 2:
            # Audio'yu 10 eşit parçaya böl ve her parçadaki chirp sayısını say
            num_bins = 10
            bin_size = audio_length // num_bins
            bin_counts = []
            
            for i in range(num_bins):
                start = i * bin_size
                end = (i + 1) * bin_size
                count = np.sum((chirps >= start) & (chirps < end))
                bin_counts.append(count)
            
            # Dağılım uniformluğunu değerlendir
            expected_count = len(chirps) / num_bins
            chi_square = np.sum((np.array(bin_counts) - expected_count)**2 / expected_count)
            
            if chi_square < 5:
                distribution = 'uniform'
            elif chi_square < 15:
                distribution = 'moderate'
            else:
                distribution = 'clustered'
        else:
            distribution = 'insufficient_data'
            bin_counts = []
        
        # Aktif dönemleri tespit et
        activity_periods = self._detect_activity_periods(chirps, audio_length)
        
        return {
            'density': density,
            'distribution': distribution,
            'bin_counts': bin_counts,
            'activity_periods': activity_periods
        }
    
    def _analyze_chirps_by_windows(self, chirps: np.ndarray, audio_length: int) -> Dict:
        """
        8 saniyelik pencereler halinde chirp analizi (Dolbear yasası için)
        
        Args:
            chirps: Chirp pozisyonları
            audio_length: Audio uzunluğu
            
        Returns:
            Pencere bazında analiz
        """
        window_size = 8  # saniye
        window_samples = int(window_size * self.sr)
        num_windows = int(np.ceil(audio_length / window_samples))
        
        window_chirp_counts = []
        window_chirp_rates = []
        
        for i in range(num_windows):
            start_sample = i * window_samples
            end_sample = min((i + 1) * window_samples, audio_length)
            window_duration = (end_sample - start_sample) / self.sr
            
            # Bu penceredeki chirp'leri say
            window_chirps = chirps[(chirps >= start_sample) & (chirps < end_sample)]
            chirp_count = len(window_chirps)
            chirp_rate = chirp_count / window_duration if window_duration > 0 else 0
            
            window_chirp_counts.append(chirp_count)
            window_chirp_rates.append(chirp_rate)
        
        # 8 saniyelik chirp sayısı (Dolbear yasası için)
        n8_values = []
        for rate in window_chirp_rates:
            n8 = rate * 8  # 8 saniyelik chirp sayısı
            n8_values.append(n8)
        
        return {
            'window_count': num_windows,
            'window_size_seconds': window_size,
            'chirp_counts_per_window': window_chirp_counts,
            'chirp_rates_per_window': window_chirp_rates,
            'n8_values': n8_values,  # Dolbear yasası için
            'mean_chirps_per_window': float(np.mean(window_chirp_counts)) if window_chirp_counts else 0,
            'std_chirps_per_window': float(np.std(window_chirp_counts)) if len(window_chirp_counts) > 1 else 0,
            'mean_n8': float(np.mean(n8_values)) if n8_values else 0,
            'consistency': 1.0 - (np.std(window_chirp_rates) / np.mean(window_chirp_rates)) if np.mean(window_chirp_rates) > 0 else 0
        }
    
    def _detect_activity_periods(self, chirps: np.ndarray, audio_length: int) -> List[Dict]:
        """
        Aktif chirp dönemlerini tespit et
        
        Args:
            chirps: Chirp pozisyonları  
            audio_length: Audio uzunluğu
            
        Returns:
            Aktif dönemler listesi
        """
        if len(chirps) < 2:
            return []
        
        # Chirp'ler arası mesafeleri hesapla
        intervals = np.diff(chirps) / self.sr
        
        # Büyük boşlukları bul (ortalama + 2*std'den büyük)
        mean_interval = np.mean(intervals)
        std_interval = np.std(intervals)
        gap_threshold = mean_interval + 2 * std_interval
        
        # Aktif dönemleri belirle
        activity_periods = []
        period_start = chirps[0]
        
        for i, interval in enumerate(intervals):
            if interval > gap_threshold:
                # Dönem sonu
                period_end = chirps[i]
                period_duration = (period_end - period_start) / self.sr
                period_chirp_count = i - len(activity_periods) + 1
                
                activity_periods.append({
                    'start_time': float(period_start / self.sr),
                    'end_time': float(period_end / self.sr),
                    'duration': float(period_duration),
                    'chirp_count': int(period_chirp_count),
                    'chirp_rate': float(period_chirp_count / period_duration) if period_duration > 0 else 0
                })
                
                # Yeni dönem başlat
                period_start = chirps[i + 1]
        
        # Son dönem
        if len(activity_periods) == 0 or period_start != chirps[-1]:
            period_end = chirps[-1]
            period_duration = (period_end - period_start) / self.sr
            remaining_chirps = len(chirps) - sum(p['chirp_count'] for p in activity_periods)
            
            activity_periods.append({
                'start_time': float(period_start / self.sr),
                'end_time': float(period_end / self.sr),
                'duration': float(period_duration),
                'chirp_count': int(remaining_chirps),
                'chirp_rate': float(remaining_chirps / period_duration) if period_duration > 0 else 0
            })
        
        return activity_periods
    
    def _create_empty_result(self, error_message: str) -> Dict:
        """
        Boş sonuç oluştur
        
        Args:
            error_message: Hata mesajı
            
        Returns:
            Boş sonuç dictionary
        """
        return {
            'success': False,
            'error': error_message,
            'total_chirps': 0,
            'chirp_rate': 0,
            'chirp_positions': [],
            'chirp_times': [],
            'duration': 0,
            'window_analysis': {
                'n8_values': [],
                'mean_n8': 0,
                'consistency': 0
            }
        }
    
    def get_chirp_summary(self, result: Dict, language: str = 'tr') -> str:
        """
        Chirp tespit sonuçlarının özeti
        
        Args:
            result: Chirp tespit sonuçları
            language: Dil ('tr', 'en')
            
        Returns:
            Özet metin
        """
        if not result.get('success', False):
            return f"❌ Chirp tespiti başarısız / Chirp detection failed: {result.get('error', 'Bilinmeyen hata')}"
        
        total_chirps = result.get('total_chirps', 0)
        chirp_rate = result.get('chirp_rate', 0)
        duration = result.get('duration', 0)
        
        window_analysis = result.get('window_analysis', {})
        mean_n8 = window_analysis.get('mean_n8', 0)
        consistency = window_analysis.get('consistency', 0)
        
        characteristics = result.get('characteristics', {})
        regularity = characteristics.get('regularity_score', 0)
        
        if language == 'tr':
            summary = f"""
🔍 **Chirp Tespit Özeti**

🔢 **Toplam Chirp:** {total_chirps}
⚡ **Chirp Oranı:** {chirp_rate:.2f} chirp/saniye
⏱️ **Süre:** {duration:.1f} saniye
🎯 **8-Saniyelik Chirp (N₈):** {mean_n8:.1f}
📊 **Tutarlılık:** {consistency:.1%}
📈 **Düzenlilik:** {regularity:.1%}
"""
        else:
            summary = f"""
🔍 **Chirp Detection Summary**

🔢 **Total Chirps:** {total_chirps}
⚡ **Chirp Rate:** {chirp_rate:.2f} chirps/second
⏱️ **Duration:** {duration:.1f} seconds
🎯 **8-Second Chirps (N₈):** {mean_n8:.1f}
📊 **Consistency:** {consistency:.1%}
📈 **Regularity:** {regularity:.1%}
"""
        
        return summary.strip()
