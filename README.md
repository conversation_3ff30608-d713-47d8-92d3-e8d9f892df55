# 🦗 Ağustos Böceği Frekans Analizi Sistemi / Cicada Frequency Analysis System

Bu proje, ağustos böceği seslerinden frekans analizi yaparak Dolbear yasası ile hava sıcaklığı tahmin eden kapsamlı bir sistemdir.

This project is a comprehensive system that estimates air temperature using Dol<PERSON>ar's law by analyzing frequencies from cicada sounds.

## ✨ Özellikler / Features

- 🧹 **Audio Denoising**: Hazır eğitilmiş Demucs modeli ile ses temizleme
- 🔍 **Chirp Detection**: LibROSA tabanlı frekans analizi
- 🌡️ **Temperature Estimation**: Dolbear yasası ile sıcaklık tahmini
- 🎮 **Web Interface**: Gradio tabanlı kullanıcı dostu arayüz
- 🌍 **Dual Language**: Türkçe-İngilizce destek
- ⚡ **GPU/CPU Support**: Otomatik cihaz seçimi

## 🚀 Kurulum / Installation

```bash
# Projeyi klonlayın
git clone https://github.com/semskurto/cicada-thermometer.git
cd cicada-thermometer

# Gerekli paketleri yükleyin
pip install -r requirements.txt

# Uygulamayı başlatın
python gradio_app.py
```

## 📁 Proje Yapısı / Project Structure

```
cicada_analyzer/
├── models/                    # Hazır modeller / Pre-trained models
│   ├── __init__.py
│   ├── denoiser_model.py     # Audio denoising modeli
│   └── frequency_analyzer.py  # Frekans analizi modeli
├── core/                     # Ana işleme mantığı / Core processing
│   ├── __init__.py
│   ├── audio_processor.py    # Ses ön işleme
│   ├── cicada_filter.py      # Ağustos böceği filtreleme
│   └── chirp_detector.py     # Chirp tespiti ve sayma
├── utils/                    # Yardımcı fonksiyonlar / Utilities
│   ├── __init__.py
│   ├── audio_utils.py        # Ses dosyası işlemleri
│   └── temperature_calc.py   # Sıcaklık hesaplama
├── gradio_app.py            # Ana Gradio interface
├── requirements.txt         # Gerekli paketler
└── README.md               # Bu dosya
```

## 🌡️ Dolbear Yasası / Dolbear's Law

Dolbear yasası, ağustos böceklerinin chirp frekansı ile hava sıcaklığı arasındaki ilişkiyi açıklar:

**Formüller / Formulas:**
- **Temel Dolbear (Celsius):** T(°C) = N₈ + 5
- **Dolbear (Fahrenheit):** T(°F) = N₁₅ + 40
- **Cicada orni Özel:** T = 1.93 × f + 20.8

## 📊 Kullanım / Usage

### 🌐 Web Arayüzü Seçenekleri

#### 1. Flask Arayüzü (Önerilen)
```bash
python flask_app.py
```
Tarayıcınızda `http://localhost:5000` adresini açın.

#### 2. Gradio Arayüzü
```bash
python gradio_app.py
```
Tarayıcınızda `http://localhost:7860` adresini açın.
**Not**: Gradio'da i18n sorunları olabilir, Flask önerilir.

#### 3. Streamlit Arayüzü (Alternatif)
```bash
streamlit run streamlit_app.py
```

### 📱 Kullanım Adımları

1. **Dosya Yükleme**: Audio dosyanızı yükleyin (.wav, .mp3, .m4a)
2. **Canlı Kayıt**: Mikrofon ile gerçek zamanlı kayıt yapın (Gradio'da)
3. **Analiz**: Denoising ve chirp detection seçeneklerini belirleyin
4. **Sonuç**: Sıcaklık tahmini ve analiz grafiklerini görüntüleyin

## 🎯 Teknik Detaylar / Technical Details

- **Frekans Aralığı**: 2-12 kHz (ağustos böceği aralığı)
- **Sample Rate**: 22.05 kHz
- **Pencere Boyutu**: 8 saniye (Dolbear yasası için)
- **Doğruluk**: ±3°C hassasiyet hedefi

## 📈 Performans / Performance

- ✅ %90+ chirp detection başarısı
- ✅ <30 saniye işleme süresi
- ✅ GPU/CPU otomatik optimizasyon
- ✅ Real-time audio processing

## 🤝 Katkıda Bulunma / Contributing

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Commit yapın (`git commit -m 'Add some AmazingFeature'`)
4. Push yapın (`git push origin feature/AmazingFeature`)
5. Pull Request açın

## 📄 Lisans / License

Bu proje MIT lisansı altında dağıtılmaktadır. Detaylar için `LICENSE` dosyasına bakın.

## 👨‍💻 Geliştirici / Developer

**Sems Kurtoglu** - [GitHub](https://github.com/semskurto)

## 🙏 Teşekkürler / Acknowledgments

- Dolbear yasası araştırmaları
- LibROSA ve PyTorch topluluğu
- Gradio geliştirici ekibi
