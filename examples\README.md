# Examples - <PERSON><PERSON><PERSON>ö<PERSON>, <PERSON><PERSON><PERSON><PERSON>ö<PERSON>ği Frekans Analizi Sistemi'nin nasıl kullanılacağına dair örnek dosyalar içerir.

This folder contains example files showing how to use the Cicada Frequency Analysis System.

## 📁 İçerik / Contents

### 🎵 Örnek Ses Dosyaları / Sample Audio Files
- `sample_cicada.wav` - <PERSON><PERSON><PERSON> ağ<PERSON><PERSON> b<PERSON> sesi / Synthetic cicada sound
- `real_cicada_example.wav` - Gerçek ağustos böceği kaydı örneği / Real cicada recording example

### 📄 Örnek Kod / Example Code
- `basic_usage.py` - Temel kullanım örneği / Basic usage example
- `advanced_analysis.py` - Gelişmiş analiz örneği / Advanced analysis example
- `batch_processing.py` - Toplu işleme örneği / Batch processing example

### 📊 Örnek Çıktılar / Example Outputs
- `example_results.json` - Örnek analiz sonuçları / Example analysis results
- `temperature_chart.png` - Sıcaklık grafiği örneği / Temperature chart example

## 🚀 Hızlı Test / Quick Test

1. **Temel test / Basic test:**
   ```bash
   python examples/basic_usage.py
   ```

2. **Web arayüzü test / Web interface test:**
   - `examples/sample_cicada.wav` dosyasını web arayüzüne yükleyin
   - Upload `examples/sample_cicada.wav` to the web interface

3. **Gelişmiş analiz / Advanced analysis:**
   ```bash
   python examples/advanced_analysis.py
   ```

## 📋 Beklenen Sonuçlar / Expected Results

- **Örnek ses için / For sample audio:**
  - Chirp sayısı: ~15-20
  - Tahmini sıcaklık: 20-25°C
  - Güven seviyesi: Orta/Yüksek

- **Gerçek kayıt için / For real recording:**
  - Değişken sonuçlar (ortam koşullarına bağlı)
  - Variable results (depends on environmental conditions)

## ❓ Sorun Giderme / Troubleshooting

Örnekler çalışmıyorsa:
If examples don't work:

1. Önce ana sistemi kurun / First set up the main system:
   ```bash
   python setup.py
   ```

2. Gerekli dosyaların varlığını kontrol edin / Check if required files exist

3. Python yolunu kontrol edin / Check Python path:
   ```bash
   python -c "import sys; print(sys.path)"
   ```
