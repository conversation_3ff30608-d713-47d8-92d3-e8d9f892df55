#!/usr/bin/env python3
"""
🦗 Ağustos Böceği Frekans Analizi Sistemi - Setup Script
Cicada Frequency Analysis System - Setup Script

Bu script sistemi kurmak ve ilk çalıştırmak için gerekli işlemleri yapar.
This script performs necessary operations to set up and run the system for the first time.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Başlangıç banner'ını yazdır"""
    banner = """
    🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗
    
    🌡️  AĞUSTOS BÖCEĞİ FREKANS ANALİZİ SİSTEMİ  🌡️
    🌡️      CICADA FREQUENCY ANALYSIS SYSTEM      🌡️
    
    📊 Dolbear Yasası ile Sıcaklık Tahmini
    📊 Temperature Estimation using Dolbear's Law
    
    👨‍💻 Geliştirici / Developer: Sems Kurtoglu
    🔗 GitHub: https://github.com/semskurto/cicada-thermometer
    
    🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗🦗
    """
    print(banner)

def check_python_version():
    """Python versiyonunu kontrol et"""
    print("🐍 Python versiyonu kontrol ediliyor / Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8 veya üzeri gerekli. Mevcut versiyon: {version.major}.{version.minor}")
        print(f"❌ Python 3.8 or higher required. Current version: {version.major}.{version.minor}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Uyumlu / Compatible")
    return True

def check_system_info():
    """Sistem bilgilerini göster"""
    print("💻 Sistem bilgileri / System information:")
    print(f"   🖥️  Platform: {platform.system()} {platform.release()}")
    print(f"   🏗️  Architecture: {platform.machine()}")
    print(f"   🐍 Python: {platform.python_version()}")
    
def create_directories():
    """Gerekli dizinleri oluştur"""
    print("📁 Dizinler oluşturuluyor / Creating directories...")
    
    directories = [
        'models',
        'cache', 
        'logs',
        'temp',
        'output'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"   ✅ {directory}/")
    
    print("✅ Dizinler hazır / Directories ready")

def install_requirements():
    """Gerekli paketleri yükle"""
    print("📦 Gerekli paketler yükleniyor / Installing required packages...")
    
    try:
        # requirements.txt dosyasını kontrol et
        if not os.path.exists('requirements.txt'):
            print("❌ requirements.txt dosyası bulunamadı / requirements.txt file not found")
            return False
        
        # Pip upgrade
        print("⬆️ pip güncelleniyor / Upgrading pip...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                      check=True, capture_output=True)
        
        # Paketleri yükle
        print("📥 Paketler yükleniyor / Installing packages...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                               check=True, capture_output=True, text=True)
        
        print("✅ Paketler başarıyla yüklendi / Packages installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Paket yükleme hatası / Package installation error:")
        print(f"   {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ Beklenmeyen hata / Unexpected error: {str(e)}")
        return False

def check_gpu_availability():
    """GPU kullanılabilirliğini kontrol et"""
    print("🎮 GPU kullanılabilirliği kontrol ediliyor / Checking GPU availability...")
    
    try:
        import torch
        
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            device_name = torch.cuda.get_device_name(0)
            print(f"✅ CUDA GPU tespit edildi / CUDA GPU detected:")
            print(f"   🎮 Device Count: {device_count}")
            print(f"   🏷️  Device Name: {device_name}")
            print(f"   💾 Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            return True
        else:
            print("ℹ️ GPU bulunamadı, CPU kullanılacak / No GPU found, will use CPU")
            return False
            
    except ImportError:
        print("⚠️ PyTorch henüz yüklenmemiş / PyTorch not yet installed")
        return False
    except Exception as e:
        print(f"⚠️ GPU kontrol hatası / GPU check error: {str(e)}")
        return False

def test_audio_libraries():
    """Ses kütüphanelerini test et"""
    print("🎵 Ses kütüphaneleri test ediliyor / Testing audio libraries...")
    
    try:
        import librosa
        import soundfile as sf
        import scipy
        
        print("✅ LibROSA - OK")
        print("✅ SoundFile - OK") 
        print("✅ SciPy - OK")
        
        # Basit bir test
        import numpy as np
        test_signal = np.sin(2 * np.pi * 440 * np.linspace(0, 1, 22050))
        mfccs = librosa.feature.mfcc(y=test_signal, sr=22050, n_mfcc=13)
        
        print(f"✅ Audio processing test - OK (MFCC shape: {mfccs.shape})")
        return True
        
    except ImportError as e:
        print(f"❌ Kütüphane import hatası / Library import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Audio test hatası / Audio test error: {str(e)}")
        return False

def test_denoiser_model():
    """Denoiser modelini test et"""
    print("🧹 Denoiser model test ediliyor / Testing denoiser model...")
    
    try:
        from denoiser import pretrained
        
        print("✅ Denoiser kütüphanesi - OK")
        print("ℹ️ Model yükleme testi atlanıyor (yavaş olabilir) / Skipping model loading test (can be slow)")
        return True
        
    except ImportError:
        print("⚠️ Denoiser kütüphanesi bulunamadı / Denoiser library not found")
        print("   Denoising özelliği devre dışı olacak / Denoising feature will be disabled")
        return False
    except Exception as e:
        print(f"⚠️ Denoiser test hatası / Denoiser test error: {str(e)}")
        return False

def run_configuration_test():
    """Konfigürasyon testini çalıştır"""
    print("⚙️ Konfigürasyon test ediliyor / Testing configuration...")
    
    try:
        from config import validate_config, create_directories
        
        # Dizinleri oluştur
        create_directories()
        
        # Konfigürasyonu doğrula
        if validate_config():
            print("✅ Konfigürasyon testi başarılı / Configuration test successful")
            return True
        else:
            print("❌ Konfigürasyon testi başarısız / Configuration test failed")
            return False
            
    except ImportError as e:
        print(f"❌ Konfigürasyon import hatası / Configuration import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Konfigürasyon test hatası / Configuration test error: {str(e)}")
        return False

def run_component_tests():
    """Ana bileşenleri test et"""
    print("🧪 Ana bileşenler test ediliyor / Testing main components...")
    
    try:
        # Audio utilities test
        from cicada_analyzer.utils.audio_utils import AudioUtils
        print("✅ AudioUtils - OK")
        
        # Temperature calculator test
        from cicada_analyzer.utils.temperature_calc import TemperatureCalculator
        temp_calc = TemperatureCalculator()
        
        # Test verisi
        test_chirp_data = {
            'chirp_rate': 15.0,
            'total_chirps': 120,
            'duration': 8.0,
            'window_chirp_rates': [14, 15, 16],
            'consistency': 0.8
        }
        
        result = temp_calc.calculate_temperature(test_chirp_data, 'basic_celsius')
        if result['valid']:
            print(f"✅ TemperatureCalculator - OK (Test result: {result['temperature']}°C)")
        else:
            print("⚠️ TemperatureCalculator - Warning")
        
        return True
        
    except ImportError as e:
        print(f"❌ Bileşen import hatası / Component import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Bileşen test hatası / Component test error: {str(e)}")
        return False

def create_sample_audio():
    """Örnek ses dosyası oluştur"""
    print("🎵 Örnek ses dosyası oluşturuluyor / Creating sample audio file...")
    
    try:
        import numpy as np
        import soundfile as sf
        
        # 10 saniyelik örnek chirp sinyali
        sr = 22050
        duration = 10
        t = np.linspace(0, duration, duration * sr)
        
        # Chirp benzeri sinyal (4-6 kHz arası)
        chirp_signal = np.sin(2 * np.pi * 5000 * t) * np.exp(-((t % 0.5) - 0.1)**2 / 0.01)
        
        # Noise ekle
        noise = np.random.normal(0, 0.1, len(chirp_signal))
        final_signal = chirp_signal + noise
        
        # Normalize
        final_signal = final_signal / np.max(np.abs(final_signal))
        
        # Kaydet
        sample_path = 'sample_cicada.wav'
        sf.write(sample_path, final_signal, sr)
        
        print(f"✅ Örnek ses dosyası oluşturuldu / Sample audio file created: {sample_path}")
        return True
        
    except Exception as e:
        print(f"⚠️ Örnek ses dosyası oluşturulamadı / Could not create sample audio: {str(e)}")
        return False

def display_usage_instructions():
    """Kullanım talimatlarını göster"""
    print("\n" + "="*60)
    print("📋 KULLANIM TALİMATLARI / USAGE INSTRUCTIONS")
    print("="*60)
    
    print("""
🚀 SİSTEMİ BAŞLATMAK İÇİN / TO START THE SYSTEM:

   python gradio_app.py

🌐 Web arayüzüne erişim / Web interface access:
   http://localhost:7860

📁 Dosya yükleme / File upload:
   • WAV, MP3, M4A formatları desteklenir / formats supported
   • En az 8 saniye ses kaydı önerilir / minimum 8 seconds recommended
   
🎤 Canlı kayıt / Live recording:
   • Mikrofon izni gerekli / microphone permission required
   • Sessiz ortamda kayıt yapın / record in quiet environment

⚙️ Ayarlar / Settings:
   • Denoising: Gürültülü ortamlar için / for noisy environments
   • Chirp Detection: Her zaman açık / always keep enabled
   • Sıcaklık yöntemi: "Temel Dolbear" önerilir / "Basic Dolbear" recommended

🔧 Sorun giderme / Troubleshooting:
   • config.py dosyasından ayarları değiştirebilirsiniz
   • Log dosyaları logs/ klasöründe
   • GPU sorunları için CPU modu kullanın
    """)

def main():
    """Ana kurulum fonksiyonu"""
    print_banner()
    
    print("🔧 Sistem kurulumu başlatılıyor / Starting system setup...\n")
    
    # Test adımları
    tests = [
        ("Python Versiyonu / Python Version", check_python_version),
        ("Sistem Bilgileri / System Info", check_system_info),
        ("Dizin Oluşturma / Directory Creation", create_directories),
        ("Paket Yükleme / Package Installation", install_requirements),
        ("GPU Kontrolü / GPU Check", check_gpu_availability),
        ("Ses Kütüphaneleri / Audio Libraries", test_audio_libraries),
        ("Denoiser Model / Denoiser Model", test_denoiser_model),
        ("Konfigürasyon / Configuration", run_configuration_test),
        ("Bileşen Testleri / Component Tests", run_component_tests),
        ("Örnek Ses / Sample Audio", create_sample_audio)
    ]
    
    success_count = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        
        try:
            if test_func():
                success_count += 1
                print(f"✅ {test_name} - Başarılı / Successful")
            else:
                print(f"⚠️ {test_name} - Uyarı / Warning")
        except Exception as e:
            print(f"❌ {test_name} - Hata / Error: {str(e)}")
    
    # Sonuç özeti
    print("\n" + "="*60)
    print("📊 KURULUM ÖZETİ / SETUP SUMMARY")
    print("="*60)
    
    print(f"✅ Başarılı testler / Successful tests: {success_count}/{total_tests}")
    print(f"⚠️ Uyarı/Hata / Warnings/Errors: {total_tests - success_count}/{total_tests}")
    
    if success_count >= total_tests * 0.8:  # %80 başarı oranı
        print("\n🎉 KURULUM BAŞARILI / SETUP SUCCESSFUL!")
        print("   Sistem kullanıma hazır / System ready for use")
        display_usage_instructions()
    elif success_count >= total_tests * 0.6:  # %60 başarı oranı
        print("\n⚠️ KURULUM KISMEN BAŞARILI / SETUP PARTIALLY SUCCESSFUL")
        print("   Bazı özellikler sınırlı olabilir / Some features may be limited")
        display_usage_instructions()
    else:
        print("\n❌ KURULUM PROBLEMLİ / SETUP HAS ISSUES")
        print("   Lütfen hataları düzeltin / Please fix the errors")
        print("   Yardım için: https://github.com/semskurto/cicada-thermometer/issues")

if __name__ == "__main__":
    main()
