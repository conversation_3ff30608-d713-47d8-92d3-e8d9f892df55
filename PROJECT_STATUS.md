# 🦗 Proje Du<PERSON> / Project Status Summary

## ✅ <PERSON>amlana<PERSON> / Completed Components

### 📁 Proje <PERSON> / Project Structure
```
cicada-thermometer/
├── cicada_analyzer/                 # Ana paket / Main package
│   ├── models/                     # AI modelleri / AI models
│   │   ├── denoiser_model.py      # Demucs denoising modeli
│   │   └── frequency_analyzer.py   # LibROSA frekans analizi
│   ├── core/                      # Ana işleme / Core processing
│   │   ├── audio_processor.py     # Ses ön işleme
│   │   ├── chirp_detector.py      # Chirp tespiti
│   │   └── cicada_filter.py       # Ana pipeline
│   └── utils/                     # Yardımcı araçlar / Utilities
│       ├── audio_utils.py         # Ses dosyası işlemleri
│       └── temperature_calc.py    # Sıcaklık hesaplama
├── examples/                      # Örnekler / Examples
│   ├── basic_usage.py            # Temel kullanım
│   └── README.md                 # Örnek açıklamaları
├── gradio_app.py                 # Ana web arayüzü / Main web interface
├── config.py                     # Konfigürasyon ayarları
├── setup.py                      # Kurulum scripti
├── test_system.py               # Sistem testi
├── requirements.txt             # Python paketleri
├── start.bat                    # Windows başlatma scripti
├── start.sh                     # Linux/Mac başlatma scripti
└── README.md                    # Ana dokümantasyon
```

### 🔧 Teknik Özellikler / Technical Features

#### 🧹 Audio Denoising
- **Model:** Facebook Demucs (DNS48/DNS64/Master64)
- **Özellikler:** GPU/CPU otomatik seçim, çoklu model desteği
- **Performans:** Gerçek zamanlı işleme kapasitesi

#### 🔍 Frekans Analizi
- **Kütüphane:** LibROSA tabanlı
- **Frekans Aralığı:** 2-12 kHz (ağustos böceği optimizeli)
- **Özellikler:** STFT, Mel spektrogram, MFCC, Spectral features
- **Görselleştirme:** 3-panel grafik (waveform, spectrogram, chirp detection)

#### 🦗 Chirp Detection
- **Algoritma:** Adaptive threshold + peak detection
- **Validasyon:** Süre, frekans, enerji kontrolleri
- **Analiz:** 8-saniyelik pencere bazında (Dolbear yasası için)
- **Metrikler:** Chirp oranı, tutarlılık, düzenlilik

#### 🌡️ Sıcaklık Hesaplama
- **Yöntemler:** 
  - Temel Dolbear (Celsius): T(°C) = N₈ + 5
  - Dolbear (Fahrenheit): T(°F) = N₁₅ + 40
  - Cicada orni Özel: T = 1.93 × f + 20.8
- **Güven Seviyeleri:** High/Medium/Low
- **Çoklu Yöntem:** Konsensüs analizi

#### 🎮 Web Arayüzü (Gradio)
- **Sekmeler:** Dosya yükleme, Canlı kayıt, Bilgi
- **Dil Desteği:** Türkçe-İngilizce çift dil
- **Özellikler:** Drag-drop upload, mikrofon kaydı, gerçek zamanlı analiz
- **Görselleştirme:** Interactive grafikler, audio player
- **Responsive:** Farklı ekran boyutları için uyumlu

### 📊 Desteklenen Formatlar / Supported Formats
- **Giriş:** WAV, MP3, M4A, FLAC, OGG
- **Çıkış:** JSON sonuçlar, PNG grafikler, işlenmiş WAV

### ⚙️ Konfigürasyon Seçenekleri / Configuration Options
- **Model Ayarları:** Denoiser model seçimi, cihaz tercihi
- **Audio İşleme:** Sample rate, pencere boyutu, frekans aralığı
- **Chirp Tespiti:** Eşik değerleri, filtreleme parametreleri
- **Sıcaklık Hesaplama:** Güven seviyeleri, yöntem seçimi
- **UI Ayarları:** Dil, tema, port konfigürasyonu

## 🎯 Kullanım Senaryoları / Usage Scenarios

### 1. 📁 Dosya Yükleme Modu
```python
# Temel kullanım
from cicada_analyzer import CicadaFilter

cicada_filter = CicadaFilter()
result = cicada_filter.process_audio_file(
    file_path="cicada_recording.wav",
    enable_denoising=True,
    enable_chirp_detection=True,
    temperature_method="basic_celsius"
)

print(f"Tahmini sıcaklık: {result['summary']['estimated_temperature']}°C")
```

### 2. 🎤 Canlı Kayıt Modu
- Mikrofondan gerçek zamanlı kayıt
- Otomatik analiz ve sonuç gösterimi
- Streaming audio desteği

### 3. 🌐 Web Arayüzü Modu
```bash
python gradio_app.py
# http://localhost:7860 adresinde açılır
```

## 📈 Performans Hedefleri / Performance Targets

| Metrik | Hedef | Açıklama |
|--------|-------|----------|
| Chirp Detection Accuracy | >90% | 8+ saniye kayıtlarda |
| Temperature Precision | ±3°C | Ideal koşullarda |
| Processing Time | <30s | Standart 10s kayıt için |
| Memory Usage | <2GB | GPU modunda |
| File Size Support | 50MB | Maksimum upload |

## 🚀 Başlatma Seçenekleri / Startup Options

### Windows
```cmd
start.bat
```

### Linux/Mac
```bash
chmod +x start.sh
./start.sh
```

### Manuel
```bash
# Kurulum
python setup.py

# Test
python test_system.py

# Başlatma
python gradio_app.py
```

## 📋 Gereksinimler / Requirements

### Sistem Gereksinimleri
- **Python:** 3.8+ (önerilen: 3.9+)
- **RAM:** 4GB minimum, 8GB önerilen
- **Disk:** 2GB boş alan
- **İşlemci:** Multi-core önerilen
- **GPU:** İsteğe bağlı (CUDA 11.0+)

### Python Paketleri
- torch>=1.9.0 (PyTorch ekosistemi)
- librosa>=0.9.0 (Audio processing)
- gradio>=4.0.0 (Web interface)
- scipy>=1.7.0 (Scientific computing)
- matplotlib>=3.5.0 (Visualization)
- denoiser>=0.1.5 (Audio denoising)

## 🔬 Bilimsel Doğruluk / Scientific Accuracy

### Dolbear Yasası İmplementasyonu
- **Orijinal Formül:** Doğru implementasyon
- **Türkiye Adaptasyonu:** Cicada orni türü için optimize
- **Validasyon:** Literatür referansları ile uyumlu

### Frekans Analizi
- **Ağustos Böceği Aralığı:** 2-12 kHz (bilimsel kaynaklara uygun)
- **Chirp Karakteristikleri:** Gerçek türlerin davranışına uygun
- **Gürültü Filtreleme:** Çevresel seslerin etkisini minimuma indir

## 🌍 Çok Dilli Destek / Multi-language Support

### UI Metinleri
- **Türkçe:** Tam destek (varsayılan)
- **İngilizce:** Tam destek
- **Dinamik Geçiş:** Runtime dil değiştirme

### Dokümantasyon
- Tüm dosyalarda çift dil desteği
- Kod yorumları ve error mesajları
- Kullanıcı rehberleri

## 🎓 Eğitim ve Araştırma Desteği / Educational and Research Support

### Öğretim Materyalleri
- Dolbear yasası açıklaması
- Ses işleme teknikleri
- Bilimsel yöntem gösterimi

### Araştırma Araçları
- Ham veri erişimi
- Parametrik analiz
- Sonuç karşılaştırması

## 🔧 Gelişmiş Özellikler / Advanced Features

### Modüler Tasarım
- Bağımsız çalışabilen bileşenler
- Kolay genişletme imkânı
- Custom model entegrasyonu

### Performans Optimizasyonu
- GPU/CPU otomatik seçimi
- Batch processing desteği
- Memory-efficient processing

### Hata Yönetimi
- Graceful degradation
- Kullanıcı dostu error mesajları
- Logging ve debugging

## 📊 Test Coverage / Test Kapsama

### Otomatik Testler
- Unit testler her bileşen için
- Integration testler
- Performance testler

### Manuel Test Senaryoları
- Farklı ses kaliteleri
- Çeşitli çevre koşulları
- Edge case'ler

## 🚀 Gelecek Planları / Future Plans

### Kısa Vadeli (v1.1)
- Mobil responsive tasarım
- Daha fazla ağustos böceği türü
- API endpoint'leri

### Orta Vadeli (v1.2)
- Machine learning model eğitimi
- Cloud deployment
- Database integration

### Uzun Vadeli (v2.0)
- Real-time streaming
- IoT cihaz desteği
- Collaborative research platform

---

## 📞 İletişim ve Destek / Contact and Support

**Geliştirici:** Sems Kurtoglu  
**GitHub:** https://github.com/semskurto/cicada-thermometer  
**İssue Tracking:** GitHub Issues  
**Dokümantasyon:** README.md ve kod içi yorumlar

---

*Bu sistem, ağustos böceği seslerinden bilimsel yöntemlerle hava sıcaklığı tahmini yapan, açık kaynak kodlu, kullanıcı dostu bir uygulamadır.*

*This system is an open-source, user-friendly application that scientifically estimates air temperature from cicada sounds.*
