#!/usr/bin/env python3
"""
🦗 Hızlı Başlangıç Scripti / Quick Start Script
Cicada Frequency Analysis System

Bu script sistemi hızlıca başlatmak için kullanılır.
This script is used to quickly start the system.
"""

import os
import sys
import subprocess
import time

def print_quick_banner():
    """Hızlı başlangıç banner'ı"""
    print("""
    🦗 Ağustos Böceği Analiz Sistemi / Cicada Analysis System
    ⚡ Hızlı Başlangıç / Quick Start
    """)

def check_requirements():
    """Temel gereksinimleri kontrol et"""
    try:
        import gradio
        import numpy
        import librosa
        print("✅ Temel kütüphaneler yüklü / Basic libraries installed")
        return True
    except ImportError as e:
        print(f"❌ Eksik kütüphane / Missing library: {str(e)}")
        print("💡 Çözüm / Solution: python setup.py")
        return False

def start_system():
    """<PERSON>ste<PERSON> başlat"""
    try:
        print("🚀 Sistem başlatılıyor / Starting system...")
        
        # Ana uygulamayı çalıştır
        subprocess.run([sys.executable, "gradio_app.py"], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 Sistem kapatılıyor / Shutting down system...")
    except subprocess.CalledProcessError:
        print("❌ Sistem başlatılamadı / Could not start system")
        print("💡 Çözüm / Solution: python setup.py")
    except FileNotFoundError:
        print("❌ gradio_app.py bulunamadı / gradio_app.py not found")
        print("💡 Doğru dizinde olduğunuzdan emin olun / Make sure you're in the correct directory")

def main():
    """Ana fonksiyon"""
    print_quick_banner()
    
    if check_requirements():
        start_system()
    else:
        print("\n❌ Sistem başlatılamıyor / Cannot start system")
        print("🔧 Önce kurulum yapın / Please run setup first:")
        print("   python setup.py")

if __name__ == "__main__":
    main()
