"""
Frequency Analyzer - Frekans analizi modeli / Frequency analysis model

Bu modül LibROSA tabanlı frekans analizi yaparak ağustos böceği seslerini
tespit eder ve chirp frekanslarını analiz eder.

Author: <PERSON><PERSON>lu
"""

import numpy as np
import librosa
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from scipy.signal import find_peaks
from typing import Dict, List, Optional
import warnings
warnings.filterwarnings('ignore')

from ..utils.audio_utils import AudioUtils

class FrequencyAnalyzer:
    """LibROSA tabanlı frekans analizi sınıfı"""
    
    # Ağustos böceği frekans özellikleri
    CICADA_CONFIG = {
        'freq_range': (2000, 12000),  # Ağustos böceği frekans aralığı (Hz)
        'dominant_freq_range': (3000, 8000),  # Ana frekans aralığı
        'chirp_duration_range': (0.1, 2.0),  # Chirp süre aralığı (saniye)
        'min_chirp_interval': 0.05,  # Min chirp arası süre
        'energy_threshold': 0.1,  # Enerji eşiği
        'peak_prominence': 0.1  # Peak belirginlik eşiği
    }
    
    def __init__(self, sr: int = 22050):
        """
        Frequency Analyzer'ı başlat
        
        Args:
            sr: Sample rate
        """
        self.sr = sr
        self.n_fft = 2048
        self.hop_length = 512
        self.window_size = 8  # Dolbear yasası için 8 saniye
        
        # Filterbank parametreleri
        self.mel_filters = 128
        self.mfcc_coeffs = 13
        
        print(f"🔊 Frequency Analyzer başlatıldı / initialized (SR: {sr}Hz)")
    
    def analyze_audio(self, audio: np.ndarray, apply_filter: bool = True) -> Dict:
        """
        Audio sinyalinin kapsamlı frekans analizi
        
        Args:
            audio: Audio sinyal
            apply_filter: Band-pass filter uygula
            
        Returns:
            Frekans analizi sonuçları
        """
        if len(audio) == 0:
            return self._create_empty_result("Boş audio sinyali / Empty audio signal")
        
        # Önişleme
        if apply_filter:
            audio = self._apply_cicada_filter(audio)
        
        # Temel özellik çıkarma
        features = self._extract_audio_features(audio)
        
        # Spektrogram analizi
        spectral_analysis = self._perform_spectral_analysis(audio)
        
        # Chirp tespiti
        chirp_analysis = self._detect_chirps(audio)
        
        # Frekans domain analizi
        frequency_analysis = self._analyze_frequency_domain(audio)
        
        # Sonuçları birleştir
        result = {
            'audio_length': len(audio),
            'duration': len(audio) / self.sr,
            'sample_rate': self.sr,
            'features': features,
            'spectral_analysis': spectral_analysis,
            'chirp_analysis': chirp_analysis,
            'frequency_analysis': frequency_analysis,
            'filter_applied': apply_filter,
            'success': True
        }
        
        return result
    
    def _extract_audio_features(self, audio: np.ndarray) -> Dict:
        """
        Temel audio özelliklerini çıkar
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Audio özellikleri
        """
        # RMS Energy
        rms = librosa.feature.rms(y=audio, hop_length=self.hop_length)[0]
        
        # Zero Crossing Rate
        zcr = librosa.feature.zero_crossing_rate(audio, hop_length=self.hop_length)[0]
        
        # Spectral Centroid
        spectral_centroids = librosa.feature.spectral_centroid(
            y=audio, sr=self.sr, hop_length=self.hop_length)[0]
        
        # Spectral Rolloff
        spectral_rolloff = librosa.feature.spectral_rolloff(
            y=audio, sr=self.sr, hop_length=self.hop_length)[0]
        
        # Spectral Bandwidth
        spectral_bandwidth = librosa.feature.spectral_bandwidth(
            y=audio, sr=self.sr, hop_length=self.hop_length)[0]
        
        # MFCC Features
        mfccs = librosa.feature.mfcc(
            y=audio, sr=self.sr, n_mfcc=self.mfcc_coeffs, 
            hop_length=self.hop_length)
        
        return {
            'rms_energy': {
                'mean': float(np.mean(rms)),
                'std': float(np.std(rms)),
                'max': float(np.max(rms))
            },
            'zero_crossing_rate': {
                'mean': float(np.mean(zcr)),
                'std': float(np.std(zcr))
            },
            'spectral_centroid': {
                'mean': float(np.mean(spectral_centroids)),
                'std': float(np.std(spectral_centroids))
            },
            'spectral_rolloff': {
                'mean': float(np.mean(spectral_rolloff)),
                'std': float(np.std(spectral_rolloff))
            },
            'spectral_bandwidth': {
                'mean': float(np.mean(spectral_bandwidth)),
                'std': float(np.std(spectral_bandwidth))
            },
            'mfcc': {
                'coefficients': mfccs.tolist(),
                'mean': np.mean(mfccs, axis=1).tolist(),
                'std': np.std(mfccs, axis=1).tolist()
            }
        }
    
    def _perform_spectral_analysis(self, audio: np.ndarray) -> Dict:
        """
        Spektrogram analizi yap
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Spektral analiz sonuçları
        """
        # STFT hesapla
        stft = librosa.stft(audio, n_fft=self.n_fft, hop_length=self.hop_length)
        magnitude = np.abs(stft)
        phase = np.angle(stft)
        
        # Frekans eksenini al
        freqs = librosa.fft_frequencies(sr=self.sr, n_fft=self.n_fft)
        times = librosa.frames_to_time(np.arange(magnitude.shape[1]), 
                                     sr=self.sr, hop_length=self.hop_length)
        
        # Ağustos böceği frekans aralığını filtrele
        freq_mask = (freqs >= self.CICADA_CONFIG['freq_range'][0]) & \
                   (freqs <= self.CICADA_CONFIG['freq_range'][1])
        
        cicada_magnitude = magnitude[freq_mask, :]
        cicada_freqs = freqs[freq_mask]
        
        # Mel spektrogram
        mel_spec = librosa.feature.melspectrogram(
            y=audio, sr=self.sr, n_fft=self.n_fft, 
            hop_length=self.hop_length, n_mels=self.mel_filters)
        
        return {
            'stft_shape': magnitude.shape,
            'frequency_range': (float(freqs[0]), float(freqs[-1])),
            'time_range': (float(times[0]), float(times[-1])),
            'cicada_freq_range': (float(cicada_freqs[0]), float(cicada_freqs[-1])),
            'cicada_magnitude_mean': float(np.mean(cicada_magnitude)),
            'cicada_magnitude_max': float(np.max(cicada_magnitude)),
            'mel_spectrogram_shape': mel_spec.shape,
            'dominant_frequency': float(cicada_freqs[np.argmax(np.mean(cicada_magnitude, axis=1))]),
            'energy_distribution': {
                'low_freq': float(np.mean(magnitude[:len(freqs)//3, :])),
                'mid_freq': float(np.mean(magnitude[len(freqs)//3:2*len(freqs)//3, :])),
                'high_freq': float(np.mean(magnitude[2*len(freqs)//3:, :]))
            }
        }
    
    def _detect_chirps(self, audio: np.ndarray) -> Dict:
        """
        Chirp tespiti ve analizi
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Chirp analizi sonuçları
        """
        # Envelope çıkarma
        envelope = self._extract_envelope(audio)
        
        # Peak detection
        peaks, peak_properties = find_peaks(
            envelope,
            height=self.CICADA_CONFIG['energy_threshold'],
            prominence=self.CICADA_CONFIG['peak_prominence'],
            distance=int(self.CICADA_CONFIG['min_chirp_interval'] * self.sr)
        )
        
        # Chirp sürelerini hesapla
        chirp_durations = self._calculate_chirp_durations(peaks, envelope)
        
        # Geçerli chirp'leri filtrele
        valid_chirps = self._filter_valid_chirps(peaks, chirp_durations)
        
        # Pencere bazında analiz (8 saniyelik)
        window_analysis = self._analyze_by_windows(valid_chirps, len(audio))
        
        # Chirp frekansını hesapla
        chirp_rate = len(valid_chirps) / (len(audio) / self.sr) if len(audio) > 0 else 0
        
        return {
            'total_chirps': len(valid_chirps),
            'chirp_rate': chirp_rate,  # chirp/saniye
            'chirp_positions': valid_chirps.tolist() if len(valid_chirps) > 0 else [],
            'chirp_times': (valid_chirps / self.sr).tolist() if len(valid_chirps) > 0 else [],
            'chirp_durations': chirp_durations,
            'window_analysis': window_analysis,
            'envelope_shape': envelope.shape,
            'peak_properties': {
                'heights': peak_properties.get('peak_heights', []).tolist(),
                'prominences': peak_properties.get('prominences', []).tolist()
            }
        }
    
    def _analyze_frequency_domain(self, audio: np.ndarray) -> Dict:
        """
        Frekans domain analizleri
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Frekans domain analizi
        """
        # FFT hesapla
        fft = np.fft.rfft(audio)
        freqs = np.fft.rfftfreq(len(audio), 1/self.sr)
        magnitude = np.abs(fft)
        power = magnitude ** 2
        
        # Ağustos böceği aralığındaki power
        freq_mask = (freqs >= self.CICADA_CONFIG['freq_range'][0]) & \
                   (freqs <= self.CICADA_CONFIG['freq_range'][1])
        
        cicada_power = power[freq_mask]
        cicada_freqs = freqs[freq_mask]
        
        # Dominant frekanslar
        dominant_indices = np.argsort(cicada_power)[-5:]  # Top 5
        dominant_freqs = cicada_freqs[dominant_indices]
        dominant_powers = cicada_power[dominant_indices]
        
        # Frekans spektrumu istatistikleri
        total_power = np.sum(cicada_power)
        mean_freq = np.sum(cicada_freqs * cicada_power) / total_power if total_power > 0 else 0
        
        return {
            'fft_length': len(fft),
            'frequency_resolution': float(freqs[1] - freqs[0]),
            'total_power_cicada_range': float(total_power),
            'mean_frequency': float(mean_freq),
            'dominant_frequencies': dominant_freqs.tolist(),
            'dominant_powers': dominant_powers.tolist(),
            'power_spectral_density': {
                'frequencies': cicada_freqs[::10].tolist(),  # Downsample for size
                'powers': cicada_power[::10].tolist()
            }
        }
    
    def _apply_cicada_filter(self, audio: np.ndarray) -> np.ndarray:
        """
        Ağustos böceği frekans aralığı için band-pass filter
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Filtrelenmiş audio
        """
        return AudioUtils.apply_bandpass_filter(
            audio, self.sr,
            self.CICADA_CONFIG['freq_range'][0],
            self.CICADA_CONFIG['freq_range'][1]
        )
    
    def _extract_envelope(self, audio: np.ndarray) -> np.ndarray:
        """
        Audio sinyalinin envelope'ını çıkar
        
        Args:
            audio: Audio sinyal
            
        Returns:
            Envelope sinyali
        """
        # Hilbert transform ile analytic signal
        analytic_signal = librosa.util.frame(audio, frame_length=512, hop_length=128)
        envelope = np.mean(np.abs(analytic_signal), axis=0)
        
        # Smoothing
        from scipy.ndimage import gaussian_filter1d
        envelope = gaussian_filter1d(envelope, sigma=2)
        
        return envelope
    
    def _calculate_chirp_durations(self, peaks: np.ndarray, envelope: np.ndarray) -> List[float]:
        """
        Chirp sürelerini hesapla
        
        Args:
            peaks: Peak pozisyonları
            envelope: Envelope sinyali
            
        Returns:
            Chirp süreleri listesi
        """
        durations = []
        
        for peak in peaks:
            # Peak etrafında süre hesapla
            start = max(0, peak - 50)
            end = min(len(envelope), peak + 50)
            
            # Threshold'un altına düştüğü noktaları bul
            threshold = envelope[peak] * 0.5
            
            # Sol taraftan threshold altına düşene kadar
            left_end = peak
            for i in range(peak, start, -1):
                if envelope[i] < threshold:
                    left_end = i
                    break
            
            # Sağ taraftan threshold altına düşene kadar  
            right_end = peak
            for i in range(peak, end):
                if envelope[i] < threshold:
                    right_end = i
                    break
            
            # Süreyi hesapla (envelope hop_length dikkate alınarak)
            hop_length_ratio = len(envelope) / (len(envelope) * self.hop_length / self.sr)
            duration = (right_end - left_end) * hop_length_ratio / self.sr
            durations.append(max(0.01, min(duration, 2.0)))  # 0.01-2.0 saniye arası
        
        return durations
    
    def _filter_valid_chirps(self, peaks: np.ndarray, durations: List[float]) -> np.ndarray:
        """
        Geçerli chirp'leri filtrele
        
        Args:
            peaks: Peak pozisyonları
            durations: Chirp süreleri
            
        Returns:
            Geçerli chirp pozisyonları
        """
        valid_mask = []
        min_dur = self.CICADA_CONFIG['chirp_duration_range'][0]
        max_dur = self.CICADA_CONFIG['chirp_duration_range'][1]
        
        for duration in durations:
            # Süre kontrolü
            duration_valid = min_dur <= duration <= max_dur
            valid_mask.append(duration_valid)
        
        return peaks[valid_mask]
    
    def _analyze_by_windows(self, chirps: np.ndarray, audio_length: int) -> Dict:
        """
        8 saniyelik pencereler halinde chirp analizi
        
        Args:
            chirps: Chirp pozisyonları
            audio_length: Audio uzunluğu
            
        Returns:
            Pencere bazında analiz
        """
        window_samples = int(self.window_size * self.sr)
        num_windows = int(np.ceil(audio_length / window_samples))
        
        window_chirp_counts = []
        window_chirp_rates = []
        
        for i in range(num_windows):
            start_sample = i * window_samples
            end_sample = min((i + 1) * window_samples, audio_length)
            
            # Bu penceredeki chirp'leri say
            window_chirps = chirps[(chirps >= start_sample) & (chirps < end_sample)]
            chirp_count = len(window_chirps)
            chirp_rate = chirp_count / self.window_size
            
            window_chirp_counts.append(chirp_count)
            window_chirp_rates.append(chirp_rate)
        
        return {
            'window_count': num_windows,
            'window_size_seconds': self.window_size,
            'chirp_counts_per_window': window_chirp_counts,
            'chirp_rates_per_window': window_chirp_rates,
            'mean_chirps_per_window': float(np.mean(window_chirp_counts)) if window_chirp_counts else 0,
            'std_chirps_per_window': float(np.std(window_chirp_counts)) if len(window_chirp_counts) > 1 else 0,
            'consistency': 1.0 - (np.std(window_chirp_rates) / np.mean(window_chirp_rates)) if np.mean(window_chirp_rates) > 0 else 0
        }
    
    def create_visualization(self, audio: np.ndarray, analysis_result: Dict) -> Figure:
        """
        Analiz sonuçlarının görselleştirilmesi
        
        Args:
            audio: Audio sinyal
            analysis_result: Analiz sonuçları
            
        Returns:
            Matplotlib figure
        """
        fig, axes = plt.subplots(3, 1, figsize=(12, 10))
        fig.suptitle('🦗 Ağustos Böceği Frekans Analizi / Cicada Frequency Analysis', fontsize=14)
        
        # Time domain waveform
        times = np.arange(len(audio)) / self.sr
        axes[0].plot(times, audio, color='blue', alpha=0.7)
        axes[0].set_title('🌊 Ses Dalgası / Audio Waveform')
        axes[0].set_xlabel('Zaman / Time (s)')
        axes[0].set_ylabel('Genlik / Amplitude')
        axes[0].grid(True, alpha=0.3)
        
        # Spectrogram
        stft = librosa.stft(audio, n_fft=self.n_fft, hop_length=self.hop_length)
        magnitude_db = librosa.amplitude_to_db(np.abs(stft))
        
        img = axes[1].imshow(magnitude_db, aspect='auto', origin='lower', 
                           extent=[0, len(audio)/self.sr, 0, self.sr/2], 
                           cmap='viridis')
        axes[1].set_title('📊 Spektrogram / Spectrogram (0-15 kHz)')
        axes[1].set_xlabel('Zaman / Time (s)')
        axes[1].set_ylabel('Frekans / Frequency (Hz)')
        axes[1].set_ylim(0, 15000)  # Ağustos böceği odaklı
        
        # Ağustos böceği frekans aralığını vurgula
        axes[1].axhline(y=self.CICADA_CONFIG['freq_range'][0], color='red', 
                       linestyle='--', alpha=0.7, label='Ağustos Böceği Aralığı')
        axes[1].axhline(y=self.CICADA_CONFIG['freq_range'][1], color='red', 
                       linestyle='--', alpha=0.7)
        axes[1].legend()
        
        plt.colorbar(img, ax=axes[1], label='Magnitude (dB)')
        
        # Chirp detection
        envelope = self._extract_envelope(audio)
        envelope_times = np.linspace(0, len(audio)/self.sr, len(envelope))
        
        axes[2].plot(envelope_times, envelope, color='green', label='Energy Envelope')
        
        # Detected chirps
        chirp_analysis = analysis_result.get('chirp_analysis', {})
        chirp_times = chirp_analysis.get('chirp_times', [])
        
        if chirp_times:
            for chirp_time in chirp_times:
                axes[2].axvline(x=chirp_time, color='red', linestyle='|', 
                              alpha=0.8, linewidth=2)
        
        axes[2].set_title(f'🔍 Chirp Tespiti / Chirp Detection (Toplam: {len(chirp_times)})')
        axes[2].set_xlabel('Zaman / Time (s)')
        axes[2].set_ylabel('Enerji / Energy')
        axes[2].legend()
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def _create_empty_result(self, error_message: str) -> Dict:
        """
        Boş sonuç oluştur
        
        Args:
            error_message: Hata mesajı
            
        Returns:
            Boş sonuç dictionary
        """
        return {
            'success': False,
            'error': error_message,
            'audio_length': 0,
            'duration': 0,
            'chirp_analysis': {
                'total_chirps': 0,
                'chirp_rate': 0,
                'chirp_positions': [],
                'chirp_times': []
            }
        }
    
    def get_analysis_summary(self, analysis_result: Dict) -> str:
        """
        Analiz sonuçlarının özeti
        
        Args:
            analysis_result: Analiz sonuçları
            
        Returns:
            Özet metin
        """
        if not analysis_result.get('success', False):
            return f"❌ Analiz başarısız: {analysis_result.get('error', 'Bilinmeyen hata')}"
        
        chirp_analysis = analysis_result.get('chirp_analysis', {})
        spectral_analysis = analysis_result.get('spectral_analysis', {})
        
        summary = f"""
📊 **Frekans Analizi Özeti / Frequency Analysis Summary**

⏱️ **Süre / Duration:** {analysis_result.get('duration', 0):.1f} saniye/seconds
🔢 **Toplam Chirp / Total Chirps:** {chirp_analysis.get('total_chirps', 0)}
⚡ **Chirp Oranı / Chirp Rate:** {chirp_analysis.get('chirp_rate', 0):.2f} chirp/saniye
🎯 **Dominant Frekans / Dominant Frequency:** {spectral_analysis.get('dominant_frequency', 0):.0f} Hz
📈 **RMS Enerji / RMS Energy:** {analysis_result.get('features', {}).get('rms_energy', {}).get('mean', 0):.4f}
🌊 **Spektral Merkez / Spectral Centroid:** {analysis_result.get('features', {}).get('spectral_centroid', {}).get('mean', 0):.0f} Hz
"""
        
        return summary.strip()
