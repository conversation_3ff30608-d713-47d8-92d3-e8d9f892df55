<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🦗 <PERSON><PERSON><PERSON><PERSON>ö<PERSON>ği Sıcaklık <PERSON></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-input:hover {
            border-color: #4CAF50;
            background: #f9f9f9;
        }
        
        .options {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 25px 0;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
        }
        
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .analyze-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .analyze-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .results {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .results.show {
            display: block;
        }
        
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .result-item strong {
            color: #333;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #f44336;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .plot-container {
            text-align: center;
            margin-top: 20px;
        }
        
        .plot-container img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .info-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .info-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .info-section p {
            line-height: 1.6;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🦗 Ağustos Böceği Sıcaklık Analizi</h1>
            <p>Dolbear Yasası ile Sıcaklık Tahmini</p>
        </div>
        
        <div class="content">
            <form id="analysisForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="audioFile">🎵 Audio Dosyası Seçin:</label>
                    <input type="file" id="audioFile" name="audio_file" accept=".wav,.mp3,.m4a,.flac" required class="file-input">
                </div>
                
                <div class="options">
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enableDenoising" name="enable_denoising" checked>
                            <label for="enableDenoising">🧹 Ses Temizleme (Denoising)</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="enableChirp" name="enable_chirp" checked>
                            <label for="enableChirp">🔍 Chirp Tespiti</label>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="tempMethod">🌡️ Sıcaklık Hesaplama Yöntemi:</label>
                    <select id="tempMethod" name="temp_method">
                        <option value="basic_celsius">Temel Dolbear (Celsius)</option>
                        <option value="cicada_orni_special">Cicada orni Özel</option>
                        <option value="dolbear_fahrenheit">Dolbear (Fahrenheit)</option>
                    </select>
                </div>
                
                <button type="submit" class="analyze-btn" id="analyzeBtn">
                    🚀 Analiz Et
                </button>
            </form>
            
            <div id="loading" class="loading" style="display: none;">
                <div class="spinner"></div>
                <p>Analiz yapılıyor, lütfen bekleyin...</p>
            </div>
            
            <div id="results" class="results">
                <h3>📊 Analiz Sonuçları</h3>
                <div id="resultContent"></div>
                <div id="plotContainer" class="plot-container"></div>
            </div>
            
            <div id="error" class="error" style="display: none;"></div>
            
            <div class="info-section">
                <h3>📚 Dolbear Yasası Hakkında</h3>
                <p>
                    <strong>Amos Dolbear (1897)</strong> tarafından keşfedilen bu yasa, ağustos böceklerinin chirp frekansı ile çevre sıcaklığı arasındaki matematiksel ilişkiyi tanımlar:
                </p>
                <p>
                    <strong>T(°C) = N₈ + 5</strong> (8 saniyedeki chirp sayısı + 5)
                </p>
                <p>
                    Ağustos böcekleri soğukkanlı (ectothermic) olduğu için metabolizmaları sıcaklığa bağlıdır. Yüksek sıcaklıklarda daha hızlı chirp yaparlar.
                </p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('analysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const error = document.getElementById('error');
            const analyzeBtn = document.getElementById('analyzeBtn');
            
            // UI durumunu güncelle
            loading.style.display = 'block';
            results.classList.remove('show');
            error.style.display = 'none';
            analyzeBtn.disabled = true;
            
            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // Başarılı sonuçları göster
                    document.getElementById('resultContent').innerHTML = `
                        <div class="result-item">
                            <strong>🌡️ Tahmin Edilen Sıcaklık:</strong> ${data.temperature.toFixed(1)}°C
                        </div>
                        <div class="result-item">
                            <strong>🔊 Tespit Edilen Chirp Sayısı:</strong> ${data.chirp_count}
                        </div>
                        <div class="result-item">
                            <strong>📊 Ortalama Chirp Frekansı:</strong> ${data.avg_frequency.toFixed(2)} Hz
                        </div>
                        <div class="result-item">
                            <strong>✅ Güven Seviyesi:</strong> ${data.confidence.toFixed(1)}%
                        </div>
                        <div class="result-item">
                            <strong>🔬 Kullanılan Yöntem:</strong> ${data.method}
                        </div>
                    `;
                    
                    // Görselleştirmeyi göster
                    document.getElementById('plotContainer').innerHTML = `
                        <img src="data:image/png;base64,${data.plot_image}" alt="Analiz Sonucu">
                    `;
                    
                    results.classList.add('show');
                } else {
                    throw new Error(data.error || 'Bilinmeyen hata');
                }
                
            } catch (err) {
                error.textContent = `❌ Hata: ${err.message}`;
                error.style.display = 'block';
            } finally {
                loading.style.display = 'none';
                analyzeBtn.disabled = false;
            }
        });
        
        // Dosya seçimi feedback
        document.getElementById('audioFile').addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            if (fileName) {
                this.style.background = '#e8f5e8';
                this.style.borderColor = '#4CAF50';
            }
        });
    </script>
</body>
</html>
