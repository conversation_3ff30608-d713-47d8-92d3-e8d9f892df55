"""
Audio Processor - Ses ön işleme / Audio preprocessing

Bu modül ses dosyalarının yükle<PERSON>, ön işlenmesi ve temel dönüşümlerini
gerçekleştirir.

Author: <PERSON><PERSON>lu
"""

import numpy as np
import librosa
from typing import Dict, Tuple, Union, Optional, List
import warnings
warnings.filterwarnings('ignore')

from ..utils.audio_utils import AudioUtils

class AudioProcessor:
    """Audio dosyalarının ön işlenmesi için sınıf"""
    
    def __init__(self, target_sr: int = 22050, normalize: bool = True):
        """
        Audio Processor'ı başlat
        
        Args:
            target_sr: Hedef sample rate
            normalize: Audio normalizasyonu uygula
        """
        self.target_sr = target_sr
        self.normalize = normalize
        self.config = AudioUtils.AUDIO_CONFIG.copy()
        self.config['target_sr'] = target_sr
        
        print(f"🎵 Audio Processor başlatıldı / initialized (SR: {target_sr}Hz)")
    
    def load_and_preprocess(self, file_path: str) -> Tuple[np.ndarray, int, Dict]:
        """
        Audio dosyasını yükle ve ön işleme yap
        
        Args:
            file_path: Audio dosya yolu
            
        Returns:
            Tuple[processed_audio, sample_rate, process_info]
        """
        try:
            # Dosya formatını kontrol et
            if not AudioUtils.validate_audio_format(file_path):
                raise ValueError(f"Desteklenmeyen dosya formatı / Unsupported file format: {file_path}")
            
            # Audio dosyasını yükle
            print(f"📂 Audio dosyası yükleniyor / Loading audio file: {file_path}")
            
            audio, sr = AudioUtils.load_audio(file_path, self.target_sr)
            
            # Ön işleme
            processed_audio, process_info = self._preprocess_audio(audio, sr)
            
            # Audio bilgilerini al
            audio_info = AudioUtils.get_audio_info(processed_audio, sr)
            
            # Process info'yu güncelle
            process_info.update({
                'file_path': file_path,
                'audio_info': audio_info,
                'preprocessing_applied': True
            })
            
            print(f"✅ Audio başarıyla işlendi / Audio processed successfully")
            print(f"   Süre / Duration: {audio_info['duration']:.1f}s")
            print(f"   Sample Rate: {sr}Hz")
            print(f"   RMS Energy: {audio_info['rms_energy']:.4f}")
            
            return processed_audio, sr, process_info
            
        except Exception as e:
            error_msg = f"Audio işleme hatası / Audio processing error: {str(e)}"
            print(f"❌ {error_msg}")
            raise RuntimeError(error_msg)
    
    def preprocess_array(self, audio: np.ndarray, sr: int) -> Tuple[np.ndarray, Dict]:
        """
        NumPy array'i ön işleme
        
        Args:
            audio: Audio array
            sr: Sample rate
            
        Returns:
            Tuple[processed_audio, process_info]
        """
        return self._preprocess_audio(audio, sr)
    
    def _preprocess_audio(self, audio: np.ndarray, sr: int) -> Tuple[np.ndarray, Dict]:
        """
        Audio ön işleme pipeline
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            
        Returns:
            Tuple[processed_audio, process_info]
        """
        original_audio = audio.copy()
        process_steps = []
        
        # 1. Normalizasyon
        if self.normalize:
            audio = AudioUtils.normalize_audio(audio, method='peak')
            process_steps.append('normalization')
        
        # 2. DC offset kaldırma
        audio = self._remove_dc_offset(audio)
        process_steps.append('dc_removal')
        
        # 3. Sessizlik kırpma (silence trimming)
        audio, trim_info = self._trim_silence(audio, sr)
        if trim_info['trimmed']:
            process_steps.append('silence_trimming')
        
        # 4. Minimum uzunluk kontrolü
        min_length = int(0.5 * sr)  # Minimum 0.5 saniye
        if len(audio) < min_length:
            audio = self._pad_audio(audio, min_length)
            process_steps.append('padding')
        
        # 5. Maksimum uzunluk kontrolü (bellek tasarrufu için)
        max_length = int(300 * sr)  # Maksimum 5 dakika
        if len(audio) > max_length:
            audio = audio[:max_length]
            process_steps.append('truncation')
        
        # İşlem bilgileri
        process_info = {
            'original_length': len(original_audio),
            'processed_length': len(audio),
            'original_duration': len(original_audio) / sr,
            'processed_duration': len(audio) / sr,
            'sample_rate': sr,
            'process_steps': process_steps,
            'trim_info': trim_info,
            'length_change_ratio': len(audio) / len(original_audio),
            'rms_original': float(np.sqrt(np.mean(original_audio**2))),
            'rms_processed': float(np.sqrt(np.mean(audio**2)))
        }
        
        return audio, process_info
    
    def _remove_dc_offset(self, audio: np.ndarray) -> np.ndarray:
        """
        DC offset'i kaldır
        
        Args:
            audio: Audio sinyal
            
        Returns:
            DC offset'i kaldırılmış audio
        """
        return audio - np.mean(audio)
    
    def _trim_silence(self, audio: np.ndarray, sr: int, 
                     threshold: float = 0.01) -> Tuple[np.ndarray, Dict]:
        """
        Başlangıç ve sondaki sessizlikleri kırp
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            threshold: Sessizlik eşiği
            
        Returns:
            Tuple[trimmed_audio, trim_info]
        """
        # Energy-based trimming
        frame_length = int(0.025 * sr)  # 25ms frames
        hop_length = int(0.010 * sr)    # 10ms hop
        
        # Frame'lere böl ve enerji hesapla
        frames = librosa.util.frame(audio, frame_length=frame_length, 
                                  hop_length=hop_length, axis=0)
        energy = np.sqrt(np.mean(frames**2, axis=0))
        
        # Threshold'un üzerindeki frame'leri bul
        active_frames = energy > threshold
        
        if not np.any(active_frames):
            # Hiç aktif frame yoksa, orijinali döndür
            return audio, {'trimmed': False, 'reason': 'no_active_frames'}
        
        # İlk ve son aktif frame'leri bul
        first_active = np.argmax(active_frames)
        last_active = len(active_frames) - 1 - np.argmax(active_frames[::-1])
        
        # Sample pozisyonlarına dönüştür
        start_sample = max(0, first_active * hop_length - frame_length)
        end_sample = min(len(audio), (last_active + 1) * hop_length + frame_length)
        
        # Kırp
        trimmed_audio = audio[start_sample:end_sample]
        
        trim_info = {
            'trimmed': True,
            'original_length': len(audio),
            'trimmed_length': len(trimmed_audio),
            'start_sample': start_sample,
            'end_sample': end_sample,
            'removed_start_sec': start_sample / sr,
            'removed_end_sec': (len(audio) - end_sample) / sr,
            'threshold_used': threshold
        }
        
        return trimmed_audio, trim_info
    
    def _pad_audio(self, audio: np.ndarray, target_length: int) -> np.ndarray:
        """
        Audio'yu hedef uzunluğa pad'le
        
        Args:
            audio: Audio sinyal
            target_length: Hedef uzunluk
            
        Returns:
            Pad'lenmiş audio
        """
        if len(audio) >= target_length:
            return audio
        
        # Sıfır padding (sonuna ekle)
        padding_length = target_length - len(audio)
        padded_audio = np.concatenate([audio, np.zeros(padding_length)])
        
        return padded_audio
    
    def split_into_segments(self, audio: np.ndarray, sr: int, 
                          segment_duration: float = 8.0, 
                          overlap: float = 0.5) -> Tuple[List[np.ndarray], List[Dict]]:
        """
        Audio'yu segment'lere böl (Dolbear yasası için 8 saniyelik)
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            segment_duration: Segment süresi (saniye)
            overlap: Overlap oranı (0-1)
            
        Returns:
            Tuple[segment_listesi, segment_info_listesi]
        """
        segment_samples = int(segment_duration * sr)
        hop_samples = int(segment_samples * (1 - overlap))
        
        segments = []
        segment_infos = []
        
        start = 0
        segment_idx = 0
        
        while start + segment_samples <= len(audio):
            # Segment'i çıkar
            segment = audio[start:start + segment_samples]
            
            # Segment bilgileri
            segment_info = {
                'index': segment_idx,
                'start_sample': start,
                'end_sample': start + segment_samples,
                'start_time': start / sr,
                'end_time': (start + segment_samples) / sr,
                'duration': segment_duration,
                'length': len(segment),
                'rms_energy': float(np.sqrt(np.mean(segment**2))),
                'max_amplitude': float(np.max(np.abs(segment)))
            }
            
            segments.append(segment)
            segment_infos.append(segment_info)
            
            start += hop_samples
            segment_idx += 1
        
        print(f"🔗 Audio {len(segments)} segment'e bölündü / divided into segments")
        print(f"   Segment süresi / duration: {segment_duration}s")
        print(f"   Overlap oranı / ratio: {overlap}")
        
        return segments, segment_infos
    
    def validate_audio_quality(self, audio: np.ndarray, sr: int) -> Dict:
        """
        Audio kalitesini değerlendir
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            
        Returns:
            Kalite değerlendirmesi
        """
        # Temel metrikler
        rms_energy = np.sqrt(np.mean(audio**2))
        peak_amplitude = np.max(np.abs(audio))
        snr_estimate = AudioUtils.estimate_noise_level(audio)
        
        # Zero crossing rate
        zcr = librosa.feature.zero_crossing_rate(audio)[0]
        mean_zcr = np.mean(zcr)
        
        # Spektral özellikler
        spectral_centroid = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
        mean_spectral_centroid = np.mean(spectral_centroid)
        
        # Clipping detection
        clipping_threshold = 0.95
        clipped_samples = np.sum(np.abs(audio) > clipping_threshold)
        clipping_ratio = clipped_samples / len(audio)
        
        # Sessizlik oranı
        silence_threshold = 0.01
        silent_samples = np.sum(np.abs(audio) < silence_threshold)
        silence_ratio = silent_samples / len(audio)
        
        # Kalite skoru hesapla (0-100)
        quality_score = 100
        
        # Penaltiler
        if rms_energy < 0.01:
            quality_score -= 30  # Çok düşük enerji
        if clipping_ratio > 0.01:
            quality_score -= 25  # Clipping
        if silence_ratio > 0.7:
            quality_score -= 20  # Çok fazla sessizlik
        if mean_zcr > 0.3:
            quality_score -= 15  # Çok yüksek ZCR (gürültü olabilir)
        
        quality_score = max(0, quality_score)
        
        # Kalite kategorisi
        if quality_score >= 80:
            quality_category = 'excellent'
            quality_desc = 'Mükemmel / Excellent'
        elif quality_score >= 60:
            quality_category = 'good'
            quality_desc = 'İyi / Good'
        elif quality_score >= 40:
            quality_category = 'fair'
            quality_desc = 'Orta / Fair'
        else:
            quality_category = 'poor'
            quality_desc = 'Zayıf / Poor'
        
        return {
            'quality_score': quality_score,
            'quality_category': quality_category,
            'quality_description': quality_desc,
            'metrics': {
                'rms_energy': float(rms_energy),
                'peak_amplitude': float(peak_amplitude),
                'snr_estimate': float(snr_estimate),
                'zero_crossing_rate': float(mean_zcr),
                'spectral_centroid': float(mean_spectral_centroid),
                'clipping_ratio': float(clipping_ratio),
                'silence_ratio': float(silence_ratio)
            },
            'warnings': self._generate_quality_warnings(
                rms_energy, clipping_ratio, silence_ratio, mean_zcr
            )
        }
    
    def _generate_quality_warnings(self, rms_energy: float, clipping_ratio: float, 
                                 silence_ratio: float, zcr: float) -> List[str]:
        """
        Kalite uyarıları oluştur
        
        Args:
            rms_energy: RMS enerji
            clipping_ratio: Clipping oranı
            silence_ratio: Sessizlik oranı
            zcr: Zero crossing rate
            
        Returns:
            Uyarı listesi
        """
        warnings = []
        
        if rms_energy < 0.01:
            warnings.append("⚠️ Düşük ses seviyesi / Low audio level")
        
        if clipping_ratio > 0.01:
            warnings.append("⚠️ Audio clipping tespit edildi / Audio clipping detected")
        
        if silence_ratio > 0.7:
            warnings.append("⚠️ Çok fazla sessizlik / Too much silence")
        
        if zcr > 0.3:
            warnings.append("⚠️ Yüksek gürültü olasılığı / High noise possibility")
        
        if len(warnings) == 0:
            warnings.append("✅ Kalite sounu yok / No quality issues")
        
        return warnings
    
    def get_processing_summary(self, process_info: Dict) -> str:
        """
        İşleme özetini formatla
        
        Args:
            process_info: İşleme bilgileri
            
        Returns:
            Formatlanmış özet
        """
        steps_tr = {
            'normalization': 'Normalizasyon',
            'dc_removal': 'DC Offset Kaldırma',
            'silence_trimming': 'Sessizlik Kırpma',
            'padding': 'Dolgu Ekleme',
            'truncation': 'Kırpma'
        }
        
        applied_steps = [steps_tr.get(step, step) for step in process_info.get('process_steps', [])]
        
        summary = f"""
📊 **Audio İşleme Özeti / Audio Processing Summary**

⏱️ **Orijinal Süre / Original Duration:** {process_info.get('original_duration', 0):.1f}s
⏱️ **İşlenmiş Süre / Processed Duration:** {process_info.get('processed_duration', 0):.1f}s
📏 **Uzunluk Değişimi / Length Change:** {process_info.get('length_change_ratio', 1):.2f}x
🔊 **RMS Orijinal / Original:** {process_info.get('rms_original', 0):.4f}
🔊 **RMS İşlenmiş / Processed:** {process_info.get('rms_processed', 0):.4f}
⚙️ **Uygulanan İşlemler / Applied Steps:** {', '.join(applied_steps)}
"""
        
        return summary.strip()
