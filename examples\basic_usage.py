#!/usr/bin/env python3
"""
🦗 Temel Kullanım Örneği / Basic Usage Example
Cicada Frequency Analysis System

Bu örnek, sistemin temel kullanımını gösterir.
This example demonstrates basic usage of the system.

Author: <PERSON><PERSON>lu
"""

import sys
import os
import numpy as np

# Ana dizini Python path'e ekle
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from cicada_analyzer import CicadaFilter
    from cicada_analyzer.utils.temperature_calc import TemperatureCalculator
    import soundfile as sf
except ImportError as e:
    print(f"❌ Import hatası / Import error: {str(e)}")
    print("💡 Çözüm / Solution: Ana dizinde 'python setup.py' çalıştırın")
    sys.exit(1)

def create_sample_cicada_sound(filename="sample_cicada.wav", duration=10):
    """
    Örnek ağustos böceği sesi oluştur
    Create sample cicada sound
    """
    print(f"🎵 Örnek ses oluşturuluyor / Creating sample sound: {filename}")
    
    sr = 22050
    t = np.linspace(0, duration, duration * sr)
    
    # Chirp benzeri sinyal (4-6 kHz arası modulasyon)
    base_freq = 5000
    chirp_signal = np.zeros_like(t)
    
    # Her 0.6 saniyede bir chirp
    chirp_interval = 0.6
    chirp_duration = 0.2
    
    for i in range(int(duration / chirp_interval)):
        start_time = i * chirp_interval
        end_time = start_time + chirp_duration
        
        mask = (t >= start_time) & (t <= end_time)
        if np.any(mask):
            # Frequency modulation
            freq_mod = base_freq + 1000 * np.sin(2 * np.pi * 10 * (t[mask] - start_time))
            chirp_signal[mask] = np.sin(2 * np.pi * freq_mod * (t[mask] - start_time))
            
            # Amplitude envelope
            envelope = np.exp(-((t[mask] - start_time - chirp_duration/2)**2) / (2 * (chirp_duration/6)**2))
            chirp_signal[mask] *= envelope
    
    # Hafif gürültü ekle
    noise = np.random.normal(0, 0.05, len(chirp_signal))
    final_signal = chirp_signal + noise
    
    # Normalize
    final_signal = final_signal / np.max(np.abs(final_signal)) * 0.8
    
    # Kaydet
    sf.write(filename, final_signal, sr)
    print(f"✅ Örnek ses kaydedildi / Sample sound saved: {filename}")
    
    return filename, sr

def analyze_audio_basic(audio_file):
    """
    Temel ses analizi yap
    Perform basic audio analysis
    """
    print(f"🔍 Analiz başlatılıyor / Starting analysis: {audio_file}")
    
    # Cicada Filter'ı başlat
    cicada_filter = CicadaFilter(sr=22050, denoiser_model='dns48')
    
    try:
        # Ses dosyasını analiz et
        result = cicada_filter.process_audio_file(
            file_path=audio_file,
            enable_denoising=False,  # Hızlı test için kapalı
            enable_chirp_detection=True,
            temperature_method='basic_celsius'
        )
        
        # Sonuçları göster
        print("\n" + "="*50)
        print("📊 ANALİZ SONUÇLARI / ANALYSIS RESULTS")
        print("="*50)
        
        if result['success']:
            summary = result['summary']
            print(f"⏱️ Süre / Duration: {summary['duration_seconds']:.1f} saniye/seconds")
            print(f"🔢 Toplam Chirp / Total Chirps: {summary['total_chirps']}")
            print(f"⚡ Chirp Oranı / Chirp Rate: {summary['chirp_rate']:.2f} chirp/saniye")
            
            if summary['estimated_temperature'] is not None:
                print(f"🌡️ Tahmini Sıcaklık / Estimated Temperature: {summary['estimated_temperature']}°C")
                print(f"📊 Güven Seviyesi / Confidence: {summary['confidence_level'].title()}")
                print(f"📋 Yöntem / Method: {summary['method_used']}")
            else:
                print("🌡️ Sıcaklık hesaplanamadı / Temperature could not be calculated")
            
            # Detaylı sonuçlar
            print(f"\n📈 Detaylı analiz özeti / Detailed analysis summary:")
            detailed_summary = cicada_filter.get_result_summary(result, 'tr')
            print(detailed_summary)
            
        else:
            print(f"❌ Analiz başarısız / Analysis failed: {result.get('error', 'Bilinmeyen hata')}")
        
        # Bellek temizliği
        cicada_filter.cleanup()
        
        return result
        
    except Exception as e:
        print(f"❌ Analiz hatası / Analysis error: {str(e)}")
        cicada_filter.cleanup()
        return None

def temperature_calculator_example():
    """
    Sıcaklık hesaplama örneği
    Temperature calculation example
    """
    print("\n" + "="*50)
    print("🌡️ SICAKLIK HESAPLAMA ÖRNEĞİ / TEMPERATURE CALCULATION EXAMPLE")
    print("="*50)
    
    # Temperature Calculator'ı başlat
    temp_calc = TemperatureCalculator()
    
    # Örnek chirp verisi
    chirp_data = {
        'chirp_rate': 16.5,  # chirp/saniye
        'total_chirps': 132,
        'duration': 8.0,
        'window_chirp_rates': [15.2, 16.8, 17.1, 16.0, 16.9],
        'consistency': 0.85
    }
    
    print("📊 Örnek chirp verisi / Sample chirp data:")
    print(f"   Chirp oranı / Chirp rate: {chirp_data['chirp_rate']} chirp/saniye")
    print(f"   Toplam chirp / Total chirps: {chirp_data['total_chirps']}")
    print(f"   Süre / Duration: {chirp_data['duration']} saniye")
    print(f"   Tutarlılık / Consistency: {chirp_data['consistency']:.1%}")
    
    # Farklı yöntemlerle hesapla
    methods = ['basic_celsius', 'cicada_orni_special', 'dolbear_fahrenheit']
    
    for method in methods:
        result = temp_calc.calculate_temperature(chirp_data, method)
        
        if result['valid']:
            print(f"\n🔬 Yöntem / Method: {result['method_description']}")
            print(f"   🌡️ Sıcaklık / Temperature: {result['temperature']}°C")
            print(f"   📊 Güven / Confidence: {result['confidence']}")
            print(f"   📈 Std Sapma / Std Dev: ±{result['std_deviation']}°C")
        else:
            print(f"\n❌ {method}: {result['error']}")
    
    # Çoklu yöntem analizi
    print(f"\n🔍 Çoklu yöntem analizi / Multi-method analysis:")
    multi_result = temp_calc.calculate_multiple_methods(chirp_data)
    
    if 'consensus' in multi_result:
        consensus = multi_result['consensus']
        if 'average_temperature' in consensus:
            print(f"   📊 Ortalama sıcaklık / Average temperature: {consensus['average_temperature']}°C")
            print(f"   📈 Yöntemler arası sapma / Inter-method deviation: ±{consensus['std_deviation']}°C")
            print(f"   🎯 Konsensüs güveni / Consensus confidence: {consensus['consensus_confidence']}")

def main():
    """Ana fonksiyon / Main function"""
    print("🦗 Temel Kullanım Örneği / Basic Usage Example")
    print("="*60)
    
    # 1. Örnek ses dosyası oluştur
    print("\n1️⃣ Örnek ses dosyası oluşturuluyor / Creating sample audio file...")
    sample_file, sr = create_sample_cicada_sound("sample_cicada.wav", duration=10)
    
    # 2. Ses dosyasını analiz et
    print(f"\n2️⃣ Ses analizi yapılıyor / Analyzing audio...")
    result = analyze_audio_basic(sample_file)
    
    # 3. Sıcaklık hesaplama örneği
    print(f"\n3️⃣ Sıcaklık hesaplama örneği / Temperature calculation example...")
    temperature_calculator_example()
    
    # 4. Sistem bilgileri
    print(f"\n4️⃣ Sistem bilgileri / System information...")
    try:
        from cicada_analyzer import CicadaFilter
        cicada_filter = CicadaFilter()
        system_info = cicada_filter.get_system_info()
        
        print("💻 Sistem durumu / System status:")
        print(f"   📡 Sample Rate: {system_info['sample_rate']} Hz")
        print(f"   🧹 Denoiser Model: {system_info['denoiser_model']}")
        print(f"   🎮 Device: {system_info['device']}")
        print(f"   🔧 CUDA Available: {system_info['cuda_available']}")
        
        cicada_filter.cleanup()
        
    except Exception as e:
        print(f"⚠️ Sistem bilgisi alınamadı / Could not get system info: {str(e)}")
    
    print(f"\n✅ Temel kullanım örneği tamamlandı / Basic usage example completed!")
    print(f"📁 Örnek dosya: {sample_file}")
    print(f"🌐 Web arayüzü için: python gradio_app.py")

if __name__ == "__main__":
    main()
