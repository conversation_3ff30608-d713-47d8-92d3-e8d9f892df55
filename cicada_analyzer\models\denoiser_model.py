"""
Audio Denoiser Model - Ses temizleme modeli / Audio denoising model

Bu modül Facebook Demucs kütüphanesini kullanarak hazır eğitilmiş modellerle
ses dosyalarından gürültü temizleme işlemi yapar.

Author: <PERSON><PERSON>lu
"""

import torch
import torch.nn.functional as F
import numpy as np
from typing import Optional, Union, Tuple
import warnings
warnings.filterwarnings('ignore')

try:
    from denoiser import pretrained
    from denoiser.demucs import Demucs
    DENOISER_AVAILABLE = True
except ImportError:
    DENOISER_AVAILABLE = False
    print("⚠️ Denoiser kütüphanesi bulunamadı. Pip install denoiser ile yükleyin.")

from ..utils.audio_utils import AudioUtils

class DenoiserModel:
    """Hazır eğitilmiş Demucs modeli ile audio denoising sınıfı"""
    
    # Mevcut model seçenekleri
    AVAILABLE_MODELS = {
        'dns48': {
            'description': 'DNS Challenge 2020 - 48kHz - Yüksek kalite',
            'sample_rate': 48000,
            'recommended': True
        },
        'dns64': {
            'description': 'DNS Challenge 2020 - 64kHz - En yüksek kalite',
            'sample_rate': 48000,
            'recommended': False  # Daha ağır
        },
        'master64': {
            'description': 'Master model - 64 channels - Profesyonel',
            'sample_rate': 16000, 
            'recommended': False
        }
    }
    
    def __init__(self, model_name: str = 'dns48', device: str = 'auto'):
        """
        Denoiser modelini başlat
        
        Args:
            model_name: Kullanılacak model ('dns48', 'dns64', 'master64')
            device: Cihaz seçimi ('cuda', 'cpu', 'auto')
        """
        if not DENOISER_AVAILABLE:
            raise ImportError("Denoiser kütüphanesi yüklü değil / Denoiser library not installed")
        
        self.model_name = model_name
        self.device = self._get_device(device)
        self.model = None
        self.is_loaded = False
        
        # Model bilgilerini al
        if model_name not in self.AVAILABLE_MODELS:
            raise ValueError(f"Desteklenmeyen model / Unsupported model: {model_name}")
        
        self.model_info = self.AVAILABLE_MODELS[model_name]
        
        print(f"🤖 Denoiser Model: {model_name}")
        print(f"📱 Device: {self.device}")
        
    def load_model(self) -> bool:
        """
        Hazır eğitilmiş modeli yükle
        
        Returns:
            Yükleme başarılı mı?
        """
        try:
            print(f"⏳ Model yükleniyor / Loading model: {self.model_name}...")
            
            # Pretrained model yükle
            self.model = pretrained.get_model(self.model_name).to(self.device)
            self.model.eval()  # Evaluation mode
            
            self.is_loaded = True
            print(f"✅ Model başarıyla yüklendi / Model loaded successfully")
            
            return True
            
        except Exception as e:
            print(f"❌ Model yükleme hatası / Model loading error: {str(e)}")
            self.is_loaded = False
            return False
    
    def denoise_audio(self, audio: Union[np.ndarray, torch.Tensor], 
                     sr: int, normalize: bool = True) -> Tuple[np.ndarray, dict]:
        """
        Audio sinyalinden gürültü temizle
        
        Args:
            audio: Giriş audio sinyali
            sr: Sample rate
            normalize: Çıktıyı normalize et
            
        Returns:
            Tuple[temizlenmiş_audio, işlem_bilgileri]
        """
        if not self.is_loaded:
            if not self.load_model():
                raise RuntimeError("Model yüklenemedi / Could not load model")
        
        # Audio'yu tensor'a dönüştür
        if isinstance(audio, np.ndarray):
            audio_tensor = torch.from_numpy(audio).float()
        else:
            audio_tensor = audio.float()
        
        # Tek boyutlu ise batch boyutu ekle
        if audio_tensor.dim() == 1:
            audio_tensor = audio_tensor.unsqueeze(0)  # [1, samples]
        
        # Model'in beklediği sample rate'e dönüştür
        original_sr = sr
        target_sr = self.model_info['sample_rate']
        
        if original_sr != target_sr:
            audio_tensor = self._resample_audio(audio_tensor, original_sr, target_sr)
        
        # GPU'ya gönder
        audio_tensor = audio_tensor.to(self.device)
        
        # Denoising işlemi
        try:
            with torch.no_grad():
                # Model çıkarımı
                denoised_tensor = self.model(audio_tensor.unsqueeze(0))  # [1, 1, samples]
                denoised_tensor = denoised_tensor.squeeze(0).squeeze(0)  # [samples]
        
        except Exception as e:
            print(f"⚠️ Denoising hatası / Denoising error: {str(e)}")
            # Hata durumunda orijinal audio'yu döndür
            denoised_tensor = audio_tensor.squeeze(0)
        
        # Orijinal sample rate'e geri dönüştür
        if original_sr != target_sr:
            denoised_tensor = self._resample_audio(denoised_tensor.unsqueeze(0), 
                                                 target_sr, original_sr).squeeze(0)
        
        # CPU'ya geri al ve NumPy'a dönüştür
        denoised_audio = denoised_tensor.cpu().numpy()
        
        # Normalize et
        if normalize:
            denoised_audio = AudioUtils.normalize_audio(denoised_audio, method='peak')
        
        # İşlem bilgileri
        process_info = {
            'model_used': self.model_name,
            'original_sr': original_sr,
            'target_sr': target_sr,
            'device': str(self.device),
            'input_shape': audio.shape if isinstance(audio, np.ndarray) else audio_tensor.shape,
            'output_shape': denoised_audio.shape,
            'snr_improvement': self._estimate_snr_improvement(
                AudioUtils.numpy_to_torch(audio), 
                torch.from_numpy(denoised_audio)
            )
        }
        
        return denoised_audio, process_info
    
    def _get_device(self, device: str) -> torch.device:
        """
        Cihaz seçimi yap
        
        Args:
            device: İstenen cihaz
            
        Returns:
            PyTorch device
        """
        if device == 'auto':
            return torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        elif device == 'cuda':
            if not torch.cuda.is_available():
                print("⚠️ CUDA bulunamadı, CPU kullanılıyor / CUDA not found, using CPU")
                return torch.device('cpu')
            return torch.device('cuda')
        else:
            return torch.device('cpu')
    
    def _resample_audio(self, audio: torch.Tensor, orig_sr: int, target_sr: int) -> torch.Tensor:
        """
        Audio sample rate dönüşümü
        
        Args:
            audio: Audio tensor
            orig_sr: Orijinal sample rate
            target_sr: Hedef sample rate
            
        Returns:
            Yeniden örneklenmiş audio
        """
        if orig_sr == target_sr:
            return audio
        
        try:
            import torchaudio.transforms as T
            resampler = T.Resample(orig_sr, target_sr)
            return resampler(audio)
        except:
            # Fallback: scipy ile resampling
            import librosa
            audio_np = audio.cpu().numpy()
            if audio_np.ndim == 2:
                audio_np = audio_np[0]  # İlk kanalı al
            resampled = librosa.resample(audio_np, orig_sr, target_sr)
            return torch.from_numpy(resampled).unsqueeze(0) if audio.dim() == 2 else torch.from_numpy(resampled)
    
    def _estimate_snr_improvement(self, original: torch.Tensor, 
                                denoised: torch.Tensor) -> float:
        """
        SNR (Signal-to-Noise Ratio) iyileşmesini tahmin et
        
        Args:
            original: Orijinal audio
            denoised: Temizlenmiş audio
            
        Returns:
            SNR iyileşmesi (dB)
        """
        try:
            # RMS hesapla
            original_rms = torch.sqrt(torch.mean(original**2))
            denoised_rms = torch.sqrt(torch.mean(denoised**2))
            
            # Gürültü tahmini (fark)
            noise_estimate = original - denoised
            noise_rms = torch.sqrt(torch.mean(noise_estimate**2))
            
            # SNR hesapla
            if noise_rms > 0:
                snr_improvement = 20 * torch.log10(denoised_rms / noise_rms)
                return float(snr_improvement.cpu())
            else:
                return 0.0
                
        except:
            return 0.0
    
    def batch_denoise(self, audio_list: list, sr: int) -> list:
        """
        Birden fazla audio sinyalini toplu olarak temizle
        
        Args:
            audio_list: Audio sinyallerinin listesi
            sr: Sample rate
            
        Returns:
            Temizlenmiş audio'ların listesi
        """
        if not self.is_loaded:
            if not self.load_model():
                raise RuntimeError("Model yüklenemedi / Could not load model")
        
        results = []
        
        for i, audio in enumerate(audio_list):
            print(f"⏳ İşleniyor / Processing {i+1}/{len(audio_list)}...")
            try:
                denoised, info = self.denoise_audio(audio, sr)
                results.append({
                    'audio': denoised,
                    'info': info,
                    'success': True
                })
            except Exception as e:
                print(f"❌ Hata / Error in batch {i+1}: {str(e)}")
                results.append({
                    'audio': audio,  # Orijinalı döndür
                    'info': {'error': str(e)},
                    'success': False
                })
        
        return results
    
    def get_model_info(self) -> dict:
        """
        Model bilgilerini al
        
        Returns:
            Model bilgileri
        """
        info = {
            'model_name': self.model_name,
            'model_description': self.model_info['description'],
            'sample_rate': self.model_info['sample_rate'],
            'device': str(self.device),
            'is_loaded': self.is_loaded,
            'recommended': self.model_info['recommended']
        }
        
        if self.is_loaded and hasattr(self.model, 'state_dict'):
            # Model parametrelerini say
            total_params = sum(p.numel() for p in self.model.parameters())
            info['total_parameters'] = total_params
            info['model_size_mb'] = total_params * 4 / (1024 * 1024)  # Float32 assumption
        
        return info
    
    def cleanup(self):
        """
        Model'i bellekten temizle
        """
        if self.model is not None:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
        self.model = None
        self.is_loaded = False
        print("🧹 Model bellekten temizlendi / Model cleaned from memory")
    
    @staticmethod
    def list_available_models() -> dict:
        """
        Mevcut modelleri listele
        
        Returns:
            Mevcut model bilgileri
        """
        return DenoiserModel.AVAILABLE_MODELS
    
    @staticmethod
    def get_recommended_model() -> str:
        """
        Önerilen model adını al
        
        Returns:
            Önerilen model adı
        """
        for model_name, info in DenoiserModel.AVAILABLE_MODELS.items():
            if info.get('recommended', False):
                return model_name
        return 'dns48'  # Varsayılan
