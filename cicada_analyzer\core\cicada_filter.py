"""
Cicada Filter - Ana pipeline / Main processing pipeline

Bu modül tüm bileşenleri birleştirerek ağustos böceği seslerini filtreler,
chirp'leri tespit eder ve sıcaklık tahminini yapar.

Author: <PERSON><PERSON>lu
"""

import numpy as np
from typing import Dict, Optional, Tuple, Union
import warnings
warnings.filterwarnings('ignore')

from ..models.denoiser_model import DenoiserModel
from ..models.frequency_analyzer import FrequencyAnalyzer
from ..core.audio_processor import AudioProcessor
from ..core.chirp_detector import ChirpDetector
from ..utils.temperature_calc import TemperatureCalculator
from ..utils.audio_utils import AudioUtils

class CicadaFilter:
    """Ana ağustos böceği analiz pipeline sınıfı"""
    
    def __init__(self, sr: int = 22050, denoiser_model: str = 'dns48'):
        """
        Cicada Filter'ı başlat
        
        Args:
            sr: Sample rate
            denoiser_model: Denoiser model adı
        """
        self.sr = sr
        self.denoiser_model_name = denoiser_model
        
        # Ana bileşenleri başlat
        self.audio_processor = AudioProcessor(target_sr=sr)
        self.frequency_analyzer = FrequencyAnalyzer(sr=sr)
        self.chirp_detector = ChirpDetector(sr=sr)
        self.temperature_calculator = TemperatureCalculator()
        
        # Denoiser model (lazy loading)
        self.denoiser_model = None
        
        print(f"🦗 Cicada Filter başlatıldı / initialized")
        print(f"   Sample Rate: {sr}Hz")
        print(f"   Denoiser Model: {denoiser_model}")
    
    def process_audio_file(self, file_path: str, 
                          enable_denoising: bool = True,
                          enable_chirp_detection: bool = True,
                          temperature_method: str = 'basic_celsius') -> Dict:
        """
        Audio dosyasını işle ve analiz et
        
        Args:
            file_path: Audio dosya yolu
            enable_denoising: Denoising uygula
            enable_chirp_detection: Chirp detection uygula
            temperature_method: Sıcaklık hesaplama yöntemi
            
        Returns:
            Kapsamlı analiz sonuçları
        """
        print(f"🎵 Audio dosyası işleniyor / Processing audio file: {file_path}")
        
        try:
            # 1. Audio yükleme ve ön işleme
            audio, sr, process_info = self.audio_processor.load_and_preprocess(file_path)
            
            # 2. Audio kalite değerlendirmesi
            quality_assessment = self.audio_processor.validate_audio_quality(audio, sr)
            
            # 3. Denoising (isteğe bağlı)
            if enable_denoising:
                audio, denoise_info = self._apply_denoising(audio, sr)
            else:
                denoise_info = {'applied': False, 'reason': 'disabled_by_user'}
            
            # 4. Frekans analizi
            frequency_analysis = self.frequency_analyzer.analyze_audio(audio, apply_filter=True)
            
            # 5. Chirp detection (isteğe bağlı)
            if enable_chirp_detection:
                chirp_analysis = self.chirp_detector.detect_chirps(audio, apply_filter=True)
            else:
                chirp_analysis = self._create_empty_chirp_result("Chirp detection disabled")
            
            # 6. Sıcaklık hesaplama
            temperature_result = self._calculate_temperature(chirp_analysis, temperature_method)
            
            # 7. Sonuçları birleştir
            result = self._compile_results(
                file_path, process_info, quality_assessment, denoise_info,
                frequency_analysis, chirp_analysis, temperature_result
            )
            
            print(f"✅ Audio analizi tamamlandı / Audio analysis completed")
            
            return result
            
        except Exception as e:
            error_msg = f"Audio işleme hatası / Audio processing error: {str(e)}"
            print(f"❌ {error_msg}")
            return self._create_error_result(file_path, error_msg)
    
    def process_audio_array(self, audio: np.ndarray, sr: int,
                           enable_denoising: bool = True,
                           enable_chirp_detection: bool = True,
                           temperature_method: str = 'basic_celsius') -> Dict:
        """
        Audio array'ini işle ve analiz et
        
        Args:
            audio: Audio array
            sr: Sample rate
            enable_denoising: Denoising uygula
            enable_chirp_detection: Chirp detection uygula
            temperature_method: Sıcaklık hesaplama yöntemi
            
        Returns:
            Kapsamlı analiz sonuçları
        """
        print(f"🎵 Audio array işleniyor / Processing audio array")
        
        try:
            # Sample rate dönüşümü gerekirse
            if sr != self.sr:
                import librosa
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.sr)
                sr = self.sr
            
            # 1. Ön işleme
            processed_audio, process_info = self.audio_processor.preprocess_array(audio, sr)
            
            # 2. Audio kalite değerlendirmesi
            quality_assessment = self.audio_processor.validate_audio_quality(processed_audio, sr)
            
            # 3. Denoising (isteğe bağlı)
            if enable_denoising:
                processed_audio, denoise_info = self._apply_denoising(processed_audio, sr)
            else:
                denoise_info = {'applied': False, 'reason': 'disabled_by_user'}
            
            # 4. Frekans analizi
            frequency_analysis = self.frequency_analyzer.analyze_audio(processed_audio, apply_filter=True)
            
            # 5. Chirp detection (isteğe bağlı)
            if enable_chirp_detection:
                chirp_analysis = self.chirp_detector.detect_chirps(processed_audio, apply_filter=True)
            else:
                chirp_analysis = self._create_empty_chirp_result("Chirp detection disabled")
            
            # 6. Sıcaklık hesaplama
            temperature_result = self._calculate_temperature(chirp_analysis, temperature_method)
            
            # 7. Sonuçları birleştir
            result = self._compile_results(
                "audio_array", process_info, quality_assessment, denoise_info,
                frequency_analysis, chirp_analysis, temperature_result
            )
            
            print(f"✅ Audio array analizi tamamlandı / Audio array analysis completed")
            
            return result
            
        except Exception as e:
            error_msg = f"Audio array işleme hatası / Audio array processing error: {str(e)}"
            print(f"❌ {error_msg}")
            return self._create_error_result("audio_array", error_msg)
    
    def _apply_denoising(self, audio: np.ndarray, sr: int) -> Tuple[np.ndarray, Dict]:
        """
        Audio denoising uygula
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            
        Returns:
            Tuple[denoised_audio, denoise_info]
        """
        try:
            # Denoiser model'i lazy load
            if self.denoiser_model is None:
                self.denoiser_model = DenoiserModel(
                    model_name=self.denoiser_model_name,
                    device='auto'
                )
            
            # Denoising uygula
            print("🧹 Denoising uygulanıyor / Applying denoising...")
            denoised_audio, denoise_info = self.denoiser_model.denoise_audio(audio, sr)
            
            denoise_info.update({
                'applied': True,
                'model_name': self.denoiser_model_name,
                'success': True
            })
            
            return denoised_audio, denoise_info
            
        except Exception as e:
            print(f"⚠️ Denoising hatası, orijinal audio kullanılıyor / Denoising error, using original audio: {str(e)}")
            return audio, {
                'applied': False,
                'error': str(e),
                'success': False
            }
    
    def _calculate_temperature(self, chirp_analysis: Dict, method: str) -> Dict:
        """
        Chirp analizinden sıcaklık hesapla
        
        Args:
            chirp_analysis: Chirp analiz sonuçları
            method: Hesaplama yöntemi
            
        Returns:
            Sıcaklık hesaplama sonuçları
        """
        if not chirp_analysis.get('success', False):
            return {
                'success': False,
                'error': 'Chirp analysis failed',
                'temperature': None
            }
        
        try:
            # Chirp verilerini hazırla
            chirp_data = {
                'chirp_rate': chirp_analysis.get('chirp_rate', 0),
                'total_chirps': chirp_analysis.get('total_chirps', 0),
                'duration': chirp_analysis.get('duration', 0),
                'window_chirp_rates': chirp_analysis.get('window_analysis', {}).get('chirp_rates_per_window', []),
                'consistency': chirp_analysis.get('window_analysis', {}).get('consistency', 0)
            }
            
            # Sıcaklık hesapla
            temperature_result = self.temperature_calculator.calculate_temperature(chirp_data, method)
            
            # Çoklu yöntem analizi
            multiple_methods = self.temperature_calculator.calculate_multiple_methods(chirp_data)
            temperature_result['multiple_methods'] = multiple_methods
            
            return temperature_result
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Temperature calculation error: {str(e)}',
                'temperature': None
            }
    
    def _compile_results(self, source: str, process_info: Dict, quality_assessment: Dict,
                        denoise_info: Dict, frequency_analysis: Dict, 
                        chirp_analysis: Dict, temperature_result: Dict) -> Dict:
        """
        Tüm analiz sonuçlarını birleştir
        
        Args:
            source: Kaynak bilgisi
            process_info: İşleme bilgileri
            quality_assessment: Kalite değerlendirmesi
            denoise_info: Denoising bilgileri
            frequency_analysis: Frekans analizi
            chirp_analysis: Chirp analizi
            temperature_result: Sıcaklık sonuçları
            
        Returns:
            Birleştirilmiş sonuçlar
        """
        # Ana sonuç yapısı
        result = {
            'success': True,
            'source': source,
            'timestamp': self._get_timestamp(),
            'processing_info': {
                'audio_processing': process_info,
                'quality_assessment': quality_assessment,
                'denoising': denoise_info
            },
            'analysis_results': {
                'frequency_analysis': frequency_analysis,
                'chirp_analysis': chirp_analysis,
                'temperature_calculation': temperature_result
            },
            'summary': self._create_analysis_summary(
                process_info, chirp_analysis, temperature_result
            ),
            'processed_audio_info': {
                'duration': process_info.get('processed_duration', 0),
                'sample_rate': self.sr,
                'quality_score': quality_assessment.get('quality_score', 0),
                'denoising_applied': denoise_info.get('applied', False)
            }
        }
        
        return result
    
    def _create_analysis_summary(self, process_info: Dict, chirp_analysis: Dict, 
                               temperature_result: Dict) -> Dict:
        """
        Analiz özetini oluştur
        
        Args:
            process_info: İşleme bilgileri
            chirp_analysis: Chirp analizi
            temperature_result: Sıcaklık sonuçları
            
        Returns:
            Analiz özeti
        """
        return {
            'duration_seconds': process_info.get('processed_duration', 0),
            'total_chirps': chirp_analysis.get('total_chirps', 0),
            'chirp_rate': chirp_analysis.get('chirp_rate', 0),
            'estimated_temperature': temperature_result.get('temperature', None),
            'temperature_unit': temperature_result.get('unit', '°C'),
            'confidence_level': temperature_result.get('confidence', 'low'),
            'method_used': temperature_result.get('method', 'unknown'),
            'analysis_successful': (
                chirp_analysis.get('success', False) and 
                temperature_result.get('valid', False)
            )
        }
    
    def _create_empty_chirp_result(self, reason: str) -> Dict:
        """
        Boş chirp sonucu oluştur
        
        Args:
            reason: Sebep
            
        Returns:
            Boş chirp sonucu
        """
        return {
            'success': False,
            'error': reason,
            'total_chirps': 0,
            'chirp_rate': 0,
            'chirp_positions': [],
            'chirp_times': [],
            'window_analysis': {
                'chirp_rates_per_window': [],
                'consistency': 0
            }
        }
    
    def _create_error_result(self, source: str, error_message: str) -> Dict:
        """
        Hata sonucu oluştur
        
        Args:
            source: Kaynak
            error_message: Hata mesajı
            
        Returns:
            Hata sonucu
        """
        return {
            'success': False,
            'source': source,
            'timestamp': self._get_timestamp(),
            'error': error_message,
            'analysis_results': {
                'frequency_analysis': {'success': False},
                'chirp_analysis': {'success': False, 'total_chirps': 0},
                'temperature_calculation': {'success': False, 'temperature': None}
            },
            'summary': {
                'analysis_successful': False,
                'error_message': error_message
            }
        }
    
    def _get_timestamp(self) -> str:
        """
        Zaman damgası al
        
        Returns:
            ISO format zaman damgası
        """
        from datetime import datetime
        return datetime.now().isoformat()
    
    def create_visualization(self, result: Dict) -> Optional[object]:
        """
        Analiz sonuçlarının görselleştirilmesi
        
        Args:
            result: Analiz sonuçları
            
        Returns:
            Matplotlib figure veya None
        """
        try:
            if not result.get('success', False):
                return None
            
            # İşlenmiş audio'yu al (bu implementasyonda sadece visualization için)
            # Gerçek implementasyonda processed audio'yu result'ta saklayabiliriz
            frequency_analysis = result.get('analysis_results', {}).get('frequency_analysis', {})
            
            if not frequency_analysis.get('success', False):
                return None
            
            # Visualization'ı frequency analyzer'dan al
            # Bu kısım gerçek audio sinyali gerektirir
            print("📊 Görselleştirme oluşturuluyor / Creating visualization...")
            return None  # Placeholder
            
        except Exception as e:
            print(f"⚠️ Görselleştirme hatası / Visualization error: {str(e)}")
            return None
    
    def get_result_summary(self, result: Dict, language: str = 'tr') -> str:
        """
        Sonuç özetini formatla
        
        Args:
            result: Analiz sonuçları
            language: Dil ('tr', 'en')
            
        Returns:
            Formatlanmış özet
        """
        if not result.get('success', False):
            error_msg = result.get('error', 'Bilinmeyen hata / Unknown error')
            return f"❌ **Analiz Başarısız / Analysis Failed**\n\n{error_msg}"
        
        summary = result.get('summary', {})
        processing_info = result.get('processing_info', {})
        temperature_calc = result.get('analysis_results', {}).get('temperature_calculation', {})
        
        if language == 'tr':
            formatted_summary = f"""
📊 **Ses Dosyası Bilgileri / Audio File Information**
⏱️ Süre / Duration: {summary.get('duration_seconds', 0):.1f} saniye/seconds
📡 Sample Rate: {self.sr} Hz
📈 Kalite Skoru / Quality Score: {processing_info.get('quality_assessment', {}).get('quality_score', 0)}/100

⚙️ **İşleme Durumu / Processing Status**
{'🧹 Denoising: ✅ Uygulandı / Applied' if processing_info.get('denoising', {}).get('applied', False) else '🧹 Denoising: ❌ Uygulanmadı / Not Applied'}
🔍 Chirp Detection: ✅ Uygulandı / Applied

🎵 **Chirp Analizi / Chirp Analysis**
🔢 Toplam Chirp / Total Chirps: {summary.get('total_chirps', 0)}
⚡ Chirp Oranı / Chirp Rate: {summary.get('chirp_rate', 0):.2f} chirp/saniye

🌡️ **Sıcaklık Tahmini / Temperature Estimation**
🌡️ Tahmini Sıcaklık / Estimated Temperature: **{summary.get('estimated_temperature', 'N/A')}°C**
📊 Güven Seviyesi / Confidence: {summary.get('confidence_level', 'low').title()}
📋 Yöntem / Method: {temperature_calc.get('method_description', 'N/A')}
"""
        else:
            formatted_summary = f"""
📊 **Audio File Information**
⏱️ Duration: {summary.get('duration_seconds', 0):.1f} seconds
📡 Sample Rate: {self.sr} Hz
📈 Quality Score: {processing_info.get('quality_assessment', {}).get('quality_score', 0)}/100

⚙️ **Processing Status**
{'🧹 Denoising: ✅ Applied' if processing_info.get('denoising', {}).get('applied', False) else '🧹 Denoising: ❌ Not Applied'}
🔍 Chirp Detection: ✅ Applied

🎵 **Chirp Analysis**
🔢 Total Chirps: {summary.get('total_chirps', 0)}
⚡ Chirp Rate: {summary.get('chirp_rate', 0):.2f} chirps/second

🌡️ **Temperature Estimation**
🌡️ Estimated Temperature: **{summary.get('estimated_temperature', 'N/A')}°C**
📊 Confidence Level: {summary.get('confidence_level', 'low').title()}
📋 Method: {temperature_calc.get('method_description', 'N/A')}
"""
        
        return formatted_summary.strip()
    
    def cleanup(self):
        """
        Bellek temizliği
        """
        if self.denoiser_model is not None:
            self.denoiser_model.cleanup()
            self.denoiser_model = None
        print("🧹 Cicada Filter temizlendi / cleaned up")
    
    def get_system_info(self) -> Dict:
        """
        Sistem bilgilerini al
        
        Returns:
            Sistem bilgileri
        """
        import torch
        
        return {
            'sample_rate': self.sr,
            'denoiser_model': self.denoiser_model_name,
            'denoiser_loaded': self.denoiser_model is not None,
            'torch_version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'components': {
                'audio_processor': True,
                'frequency_analyzer': True,
                'chirp_detector': True,
                'temperature_calculator': True
            }
        }
