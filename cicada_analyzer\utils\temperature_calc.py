"""
Temperature Calculator - Sıcaklık hesaplama / Temperature calculation

Bu modül Dolbear yasası ve türevlerini kullanarak ağustos böceği chirp frekansından
hava sıcaklığını hesaplar.

Author: Se<PERSON> Kurt<PERSON>lu
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import statistics

class TemperatureCalculator:
    """Dolbear yasası ile sıcaklık hesaplama sınıfı"""
    
    # Dolbear yasası formülleri
    DOLBEAR_FORMULAS = {
        'basic_celsius': {
            'formula': lambda n8: n8 + 5,
            'description': 'Temel Dolbear (Celsius): T(°C) = N₈ + 5',
            'window_size': 8  # 8 saniyelik pencere
        },
        'dolbear_fahrenheit': {
            'formula': lambda n15: n15 + 40,
            'description': 'Dolbear (Fahrenheit): T(°F) = N₁₅ + 40',
            'window_size': 15  # 15 saniyelik pencere
        },
        'cicada_orni_special': {
            'formula': lambda f: 1.93 * f + 20.8,
            'description': 'Cicada orni Özel: T = 1.93 × f + 20.8',
            'window_size': 8  # 8 saniyelik pencere
        },
        'snowy_tree_cricket': {
            'formula': lambda n13: (n13 + 40) / 1.8,  # Fahrenheit to Celsius
            'description': 'Snowy Tree Cricket: T(°C) = (N₁₃ + 40) / 1.8',
            'window_size': 13
        }
    }
    
    # Güven seviyesi eşikleri
    CONFIDENCE_THRESHOLDS = {
        'high': {'min_chirps': 20, 'max_std': 2.0, 'min_duration': 8},
        'medium': {'min_chirps': 10, 'max_std': 4.0, 'min_duration': 5},
        'low': {'min_chirps': 5, 'max_std': 6.0, 'min_duration': 3}
    }
    
    def __init__(self):
        """Temperature Calculator'ı başlat"""
        self.results_history = []
    
    def calculate_temperature(self, chirp_data: Dict, method: str = 'basic_celsius') -> Dict:
        """
        Chirp verilerinden sıcaklığı hesapla
        
        Args:
            chirp_data: Chirp analiz sonuçları
            method: Kullanılacak formül ('basic_celsius', 'cicada_orni_special', etc.)
            
        Returns:
            Sıcaklık hesaplama sonuçları
        """
        if method not in self.DOLBEAR_FORMULAS:
            raise ValueError(f"Bilinmeyen metod / Unknown method: {method}")
        
        formula_info = self.DOLBEAR_FORMULAS[method]
        formula = formula_info['formula']
        
        # Chirp oranını al
        chirp_rate = chirp_data.get('chirp_rate', 0)
        total_chirps = chirp_data.get('total_chirps', 0)
        duration = chirp_data.get('duration', 0)
        
        if chirp_rate == 0 or total_chirps == 0:
            return self._create_error_result("Chirp verisi bulunamadı / No chirp data found")
        
        # Metoda göre hesaplama
        try:
            if method == 'basic_celsius':
                # 8 saniyelik chirp sayısını hesapla
                n8 = chirp_rate * 8
                temperature = formula(n8)
                unit = '°C'
                
            elif method == 'dolbear_fahrenheit':
                # 15 saniyelik chirp sayısını hesapla
                n15 = chirp_rate * 15
                temp_f = formula(n15)
                temperature = (temp_f - 32) * 5/9  # Celsius'a dönüştür
                unit = '°C'
                
            elif method == 'cicada_orni_special':
                # Doğrudan frekans kullan
                temperature = formula(chirp_rate)
                unit = '°C'
                
            elif method == 'snowy_tree_cricket':
                # 13 saniyelik chirp sayısını hesapla
                n13 = chirp_rate * 13
                temperature = formula(n13)
                unit = '°C'
                
            else:
                temperature = 0
                unit = '°C'
            
            # Güven seviyesini hesapla
            confidence = self._calculate_confidence(chirp_data)
            
            # Standart sapma hesapla (birden fazla pencere varsa)
            std_dev = self._calculate_std_deviation(chirp_data)
            
            result = {
                'temperature': round(temperature, 1),
                'unit': unit,
                'method': method,
                'method_description': formula_info['description'],
                'confidence': confidence,
                'std_deviation': std_dev,
                'chirp_rate': chirp_rate,
                'total_chirps': total_chirps,
                'duration': duration,
                'valid': True,
                'error': None
            }
            
            # Sonuçları geçmişe ekle
            self.results_history.append(result)
            
            return result
            
        except Exception as e:
            return self._create_error_result(f"Hesaplama hatası / Calculation error: {str(e)}")
    
    def calculate_multiple_methods(self, chirp_data: Dict) -> Dict:
        """
        Birden fazla yöntemle sıcaklık hesapla ve karşılaştır
        
        Args:
            chirp_data: Chirp analiz sonuçları
            
        Returns:
            Tüm yöntemlerin sonuçları
        """
        results = {}
        temperatures = []
        
        for method in self.DOLBEAR_FORMULAS.keys():
            try:
                result = self.calculate_temperature(chirp_data, method)
                results[method] = result
                if result['valid']:
                    temperatures.append(result['temperature'])
            except Exception as e:
                results[method] = self._create_error_result(str(e))
        
        # Ortalama ve varyans hesapla
        if temperatures:
            avg_temp = statistics.mean(temperatures)
            std_temp = statistics.stdev(temperatures) if len(temperatures) > 1 else 0
            
            consensus = {
                'average_temperature': round(avg_temp, 1),
                'std_deviation': round(std_temp, 1),
                'method_count': len(temperatures),
                'range': (round(min(temperatures), 1), round(max(temperatures), 1)),
                'consensus_confidence': self._get_consensus_confidence(std_temp)
            }
        else:
            consensus = {
                'average_temperature': None,
                'error': 'Hiçbir yöntem sonuç veremedi / No method could produce results'
            }
        
        return {
            'individual_results': results,
            'consensus': consensus
        }
    
    def _calculate_confidence(self, chirp_data: Dict) -> str:
        """
        Hesaplama güven seviyesini belirle
        
        Args:
            chirp_data: Chirp analiz sonuçları
            
        Returns:
            Güven seviyesi ('high', 'medium', 'low')
        """
        total_chirps = chirp_data.get('total_chirps', 0)
        duration = chirp_data.get('duration', 0)
        chirp_consistency = chirp_data.get('consistency', 0)  # Chirp tutarlılığı
        
        # Yüksek güven
        if (total_chirps >= self.CONFIDENCE_THRESHOLDS['high']['min_chirps'] and
            duration >= self.CONFIDENCE_THRESHOLDS['high']['min_duration'] and
            chirp_consistency > 0.8):
            return 'high'
        
        # Orta güven
        elif (total_chirps >= self.CONFIDENCE_THRESHOLDS['medium']['min_chirps'] and
              duration >= self.CONFIDENCE_THRESHOLDS['medium']['min_duration'] and
              chirp_consistency > 0.6):
            return 'medium'
        
        # Düşük güven
        else:
            return 'low'
    
    def _calculate_std_deviation(self, chirp_data: Dict) -> float:
        """
        Chirp oranları arasındaki standart sapma
        
        Args:
            chirp_data: Chirp analiz sonuçları
            
        Returns:
            Standart sapma
        """
        chirp_rates = chirp_data.get('window_chirp_rates', [])
        
        if len(chirp_rates) > 1:
            return round(statistics.stdev(chirp_rates), 1)
        else:
            return 0.0
    
    def _get_consensus_confidence(self, std_temp: float) -> str:
        """
        Konsensüs güven seviyesi belirle
        
        Args:
            std_temp: Yöntemler arası standart sapma
            
        Returns:
            Konsensüs güven seviyesi
        """
        if std_temp <= 2.0:
            return 'high'
        elif std_temp <= 4.0:
            return 'medium'
        else:
            return 'low'
    
    def _create_error_result(self, error_message: str) -> Dict:
        """
        Hata sonucu oluştur
        
        Args:
            error_message: Hata mesajı
            
        Returns:
            Hata sonucu dictionary
        """
        return {
            'temperature': None,
            'unit': '°C',
            'method': None,
            'confidence': 'low',
            'std_deviation': 0,
            'valid': False,
            'error': error_message
        }
    
    def get_temperature_range_info(self, temperature: float) -> Dict:
        """
        Sıcaklık aralığı hakkında bilgi ver
        
        Args:
            temperature: Hesaplanan sıcaklık
            
        Returns:
            Sıcaklık aralığı bilgileri
        """
        if temperature is None:
            return {'category': 'unknown', 'description': 'Sıcaklık hesaplanamadı'}
        
        if temperature < 10:
            return {
                'category': 'cold',
                'description': 'Soğuk / Cold',
                'note': 'Ağustos böcekleri genellikle bu sıcaklıkta aktif değildir'
            }
        elif temperature < 20:
            return {
                'category': 'cool',
                'description': 'Serin / Cool',
                'note': 'Düşük aktivite beklenir'
            }
        elif temperature < 30:
            return {
                'category': 'optimal',
                'description': 'Optimal / Optimal',
                'note': 'Ağustos böcekleri için ideal sıcaklık aralığı'
            }
        elif temperature < 40:
            return {
                'category': 'warm',
                'description': 'Sıcak / Warm',
                'note': 'Yüksek aktivite beklenir'
            }
        else:
            return {
                'category': 'hot',
                'description': 'Çok Sıcak / Very Hot',
                'note': 'Aşırı sıcaklık, doğruluk azalabilir'
            }
    
    def format_result_for_display(self, result: Dict, language: str = 'tr') -> str:
        """
        Sonucu görüntüleme için formatla
        
        Args:
            result: Hesaplama sonucu
            language: Dil ('tr', 'en')
            
        Returns:
            Formatlanmış sonuç metni
        """
        if not result['valid']:
            return f"❌ Hata / Error: {result['error']}"
        
        temp = result['temperature']
        confidence = result['confidence']
        std_dev = result['std_deviation']
        
        # Güven seviyesi emojileri
        confidence_emojis = {'high': '🟢', 'medium': '🟡', 'low': '🔴'}
        confidence_emoji = confidence_emojis.get(confidence, '⚪')
        
        if language == 'tr':
            confidence_tr = {'high': 'Yüksek', 'medium': 'Orta', 'low': 'Düşük'}
            conf_text = confidence_tr.get(confidence, confidence)
            
            formatted = f"""
🌡️ **Tahmini Sıcaklık:** {temp}°C
{confidence_emoji} **Güven Seviyesi:** {conf_text}
📊 **Standart Sapma:** ±{std_dev}°C
⚡ **Chirp Oranı:** {result['chirp_rate']:.1f} chirp/saniye
🔢 **Toplam Chirp:** {result['total_chirps']}
⏱️ **Süre:** {result['duration']:.1f} saniye
📋 **Yöntem:** {result['method_description']}
"""
        else:
            formatted = f"""
🌡️ **Estimated Temperature:** {temp}°C
{confidence_emoji} **Confidence Level:** {confidence.title()}
📊 **Standard Deviation:** ±{std_dev}°C
⚡ **Chirp Rate:** {result['chirp_rate']:.1f} chirps/second
🔢 **Total Chirps:** {result['total_chirps']}
⏱️ **Duration:** {result['duration']:.1f} seconds
📋 **Method:** {result['method_description']}
"""
        
        return formatted.strip()
    
    def get_statistics(self) -> Dict:
        """
        Geçmiş hesaplamaların istatistiklerini al
        
        Returns:
            İstatistik bilgileri
        """
        if not self.results_history:
            return {'total_calculations': 0}
        
        valid_results = [r for r in self.results_history if r['valid']]
        
        if not valid_results:
            return {'total_calculations': len(self.results_history), 'valid_calculations': 0}
        
        temperatures = [r['temperature'] for r in valid_results]
        
        return {
            'total_calculations': len(self.results_history),
            'valid_calculations': len(valid_results),
            'average_temperature': round(statistics.mean(temperatures), 1),
            'min_temperature': round(min(temperatures), 1),
            'max_temperature': round(max(temperatures), 1),
            'std_deviation': round(statistics.stdev(temperatures) if len(temperatures) > 1 else 0, 1),
            'confidence_distribution': {
                'high': len([r for r in valid_results if r['confidence'] == 'high']),
                'medium': len([r for r in valid_results if r['confidence'] == 'medium']),
                'low': len([r for r in valid_results if r['confidence'] == 'low'])
            }
        }
