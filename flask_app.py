"""
🦗 Flask Tabanlı Ağustos Böceği Analiz Arayüzü
Flask-based Cicada Analysis Interface

Gradio i18n sorunlarını önlemek için Flask kullanımı
"""

from flask import Flask, render_template, request, jsonify, send_file
import os
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # GUI olmadan çalışması için
import io
import base64
import warnings
warnings.filterwarnings('ignore')

# Cicada analyzer import
try:
    from cicada_analyzer.core.cicada_filter import CicadaFilter
    cicada_filter = None
    
    def initialize_system():
        global cicada_filter
        if cicada_filter is None:
            print("🦗 Sistem başlatılıyor...")
            cicada_filter = CicadaFilter()
            print("✅ Sistem hazır!")
        return cicada_filter
    
except ImportError as e:
    print(f"❌ Import hatası: {e}")
    cicada_filter = None

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

@app.route('/')
def index():
    """Ana sayfa"""
    return render_template('index.html')

@app.route('/analyze', methods=['POST'])
def analyze_audio():
    """Audio analiz endpoint"""
    
    try:
        # Dosya kontrolü
        if 'audio_file' not in request.files:
            return jsonify({'error': 'Audio dosyası bulunamadı'}), 400
        
        file = request.files['audio_file']
        if file.filename == '':
            return jsonify({'error': 'Dosya seçilmedi'}), 400
        
        # Parametreleri al
        enable_denoising = request.form.get('enable_denoising', 'true').lower() == 'true'
        enable_chirp = request.form.get('enable_chirp', 'true').lower() == 'true'
        temp_method = request.form.get('temp_method', 'basic_celsius')
        
        # Geçici dosya kaydet
        temp_path = os.path.join('temp', file.filename)
        os.makedirs('temp', exist_ok=True)
        file.save(temp_path)
        
        # Sistem başlat
        filter_obj = initialize_system()
        if filter_obj is None:
            return jsonify({'error': 'Sistem başlatılamadı'}), 500
        
        # Analiz yap
        result = filter_obj.process_audio_file(
            file_path=temp_path,
            enable_denoising=enable_denoising,
            enable_chirp_detection=enable_chirp,
            temperature_method=temp_method
        )
        
        # Geçici dosyayı sil
        try:
            os.remove(temp_path)
        except:
            pass
        
        if result['success']:
            temp_info = result['temperature_info']
            
            # Görselleştirme oluştur
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"Sıcaklık: {temp_info['estimated_temperature']:.1f}°C", 
                   ha='center', va='center', fontsize=24, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            ax.set_title("🦗 Ağustos Böceği Sıcaklık Analizi", fontsize=16)
            
            # Plot'u base64'e çevir
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', bbox_inches='tight', dpi=150)
            img_buffer.seek(0)
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
            plt.close()
            
            return jsonify({
                'success': True,
                'temperature': temp_info['estimated_temperature'],
                'chirp_count': temp_info['chirp_count'],
                'avg_frequency': temp_info['avg_chirp_frequency'],
                'confidence': temp_info['confidence_level'],
                'method': temp_method,
                'plot_image': img_base64
            })
        else:
            return jsonify({'error': result.get('error', 'Bilinmeyen hata')}), 500
            
    except Exception as e:
        return jsonify({'error': f'Sistem hatası: {str(e)}'}), 500

@app.route('/health')
def health_check():
    """Sistem durumu kontrolü"""
    return jsonify({
        'status': 'healthy',
        'system_ready': cicada_filter is not None
    })

if __name__ == '__main__':
    print("🦗 Flask Cicada Analysis başlatılıyor...")
    
    # Sistem başlat
    initialize_system()
    
    print("🌐 Web sunucusu başlatılıyor...")
    print("📱 Arayüz: http://127.0.0.1:5000")
    
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=False,
        threaded=True
    )
