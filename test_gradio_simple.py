"""
Basit Gradio Test Arayüzü - Siyah Ekran Sorunu Testi
Simple Gradio Test Interface - Black Screen Issue Test
"""

import gradio as gr

def test_function(text):
    return f"✅ Girdi: {text} - Test başarılı! Gradio çalışıyor."

# Basit test arayüzü
with gr.Blocks(
    title="🧪 Gradio Test", 
    css=".gradio-container { background: white !important; }"
) as demo:
    gr.<PERSON><PERSON>("# 🧪 Gradio Test Arayüzü")
    gr.Markdown("Bu arayüz Gradio'nun düzgün çalışıp çalışmadığını test eder.")
    
    with gr.Row():
        with gr.Column():
            input_text = gr.Textbox(
                label="Test Girdi", 
                placeholder="Bir şeyler yazın...",
                value="Merhaba Gradio!"
            )
            test_btn = gr.But<PERSON>("🚀 Test Et", variant="primary")
        
        with gr.<PERSON>umn():
            output_text = gr.Textbox(label="Test Çıktı", interactive=False)
    
    test_btn.click(test_function, inputs=input_text, outputs=output_text)
    
    # Otomatik test
    demo.load(lambda: "🎉 Gradio başarıyla yüklendi!", outputs=output_text)

if __name__ == "__main__":
    print("🧪 Gradio test arayüzü başlatılıyor...")
    demo.launch(
        server_name="127.0.0.1",
        server_port=7861,
        share=False,
        debug=True,
        show_error=True
    )
