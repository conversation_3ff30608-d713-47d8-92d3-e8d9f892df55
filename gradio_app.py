"""
🦗 Ağustos Böceği Frekans Analizi Sistemi - Gradio Interface
Cicada Frequency Analysis System - Gradio Interface

Bu modül, ağ<PERSON><PERSON> b<PERSON><PERSON>ği seslerinden frekans analizi yaparak Dolbear yasası ile
hava sıcaklığı tahmin eden bilimsel web tabanlı kullanıcı arayüzünü sağlar.

Dolbear Yasası (1897):
- Ağustos böcekleri soğukkanlı canlılar olduğu için metabolizmaları çevre sıcaklığına bağlıdır
- Yüksek sıcaklıklarda daha hızlı chirp yaparlar
- T(°C) = N₈ + 5 (8 saniyedeki chirp sayısı + 5)

Author: Sems Kurtoglu
"""

import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from cicada_analyzer import CicadaFilter

# Global değişkenler
cicada_filter: CicadaFilter | None = None
current_language = 'tr'

# UI Metinleri
UI_TEXTS = {
    'tr': {
        'title': '🦗 Ağustos Böceği Frekans Analizi Sistemi',
        'subtitle': 'Dolbear Yasası ile Hava Sıcaklığı Tahmini',
        'file_upload_tab': '📁 Dosya Yükleme',
        'live_record_tab': '🎤 Canlı Kayıt',
        'info_tab': 'ℹ️ Bilgi',
        'upload_audio': 'Ses Dosyası Yükleyin',
        'upload_desc': 'WAV, MP3, M4A formatlarında ses dosyası yükleyebilirsiniz',
        'enable_denoising': '🧹 Ses Temizleme (Denoising) Uygula',
        'enable_chirp': '🔍 Chirp Tespiti Uygula',
        'temp_method': '🌡️ Sıcaklık Hesaplama Yöntemi',
        'analyze_btn': '🚀 Analiz Et',
        'record_audio': 'Ses Kaydet',
        'record_desc': 'Mikrofondan canlı ses kaydı yapın (en az 8 saniye)',
        'status_title': '📊 Analiz Durumu',
        'result_title': '📈 Sonuçlar',
        'viz_title': '📊 Grafik Analizi',
        'processed_audio_title': '🎵 İşlenmiş Ses',
        'dolbear_info': """
        ## 🦗 Dolbear Yasası Hakkında / About Dolbear's Law

        **Tarihçe / History:**
        Dolbear yasası, 1897'de Amerikalı fizikçi Amos Dolbear tarafından keşfedilen, ağustos böceği chirp frekansı ile hava sıcaklığı arasındaki matematiksel ilişkiyi açıklayan yasadır.

        ### 📐 Matematiksel Formüller / Mathematical Formulas:
        - **Temel Dolbear (Celsius):** T(°C) = N₈ + 5
          - N₈: 8 saniyedeki chirp sayısı
        - **Dolbear (Fahrenheit):** T(°F) = N₁₅ + 40
          - N₁₅: 15 saniyedeki chirp sayısı
        - **Cicada orni Özel:** T = 1.93 × f + 20.8
          - f: Chirp frekansı (Hz)

        ### 🔬 Bilimsel Temel / Scientific Basis:
        **Termoregülasyon:** Ağustos böcekleri soğukkanlı (ektotermik) canlılar olduğu için, vücut sıcaklıkları çevre sıcaklığına bağlıdır.

        **Metabolik İlişki:** Yüksek sıcaklıklarda:
        - Enzim aktivitesi artar
        - Metabolizma hızlanır
        - Kas kasılma hızı artar
        - Chirp frekansı yükselir

        **Akustik Mekanizma:** Chirp sesi, ağustos böceğinin ön kanatlarını sürtmesiyle oluşur. Sıcaklık arttıkça kas kasılma hızı artar.

        ### 🎯 Doğruluk ve Sınırlamalar / Accuracy and Limitations:
        - **Hassasiyet:** Uygun koşullarda ±2-3°C
        - **Sıcaklık Aralığı:** 13-38°C arası en doğru
        - **Tür Bağımlılığı:** Farklı türler için farklı katsayılar
        - **Çevresel Faktörler:** Nem, rüzgar, gürültü etkisi
        """,
        'system_info': """
        ## ⚙️ Sistem Özellikleri / System Features

        ### 🧠 AI Modelleri ve Algoritmalar / AI Models and Algorithms:

        **1. Audio Denoising:**
        - **Model:** Facebook Demucs (DNS48/DNS64/Master64)
        - **Teknoloji:** Deep Neural Network tabanlı gürültü temizleme
        - **Amaç:** Çevresel gürültüyü kaldırarak ağustos böceği sesini izole etme

        **2. Frekans Analizi / Frequency Analysis:**
        - **Kütüphane:** LibROSA (Librosa for Audio Analysis)
        - **Yöntem:** STFT (Short-Time Fourier Transform)
        - **Özellikler:** Mel spektrogram, MFCC, Spectral centroid
        - **Band-pass Filter:** Butterworth 2-12 kHz (ağustos böceği aralığı)

        **3. Chirp Detection:**
        - **Algoritma:** Adaptive threshold ile peak detection
        - **Kütüphane:** SciPy signal processing
        - **Parametreler:** Minimum chirp aralığı, amplitude threshold

        ### 📊 Teknik Spesifikasyonlar / Technical Specifications:
        - **Sample Rate:** 22.05 kHz (Nyquist teoremi gereği 11 kHz'e kadar analiz)
        - **Frekans Aralığı:** 2-12 kHz (Cicada orni optimal aralığı)
        - **Analiz Penceresi:** 8 saniye (Dolbear yasası standardı)
        - **Minimum Kayıt Süresi:** 8 saniye
        - **Desteklenen Formatlar:** WAV, MP3, M4A
        - **İşleme Hızı:** <30 saniye (CPU), <10 saniye (GPU)
        - **Platform:** CPU ve GPU otomatik optimizasyon

        ### 🔬 Bilimsel Doğrulama / Scientific Validation:
        - **Referans:** Dolbear, A. E. (1897). "The cricket as a thermometer"
        - **Doğruluk:** ±2-3°C (optimal koşullarda)
        - **Test Edilen Türler:** Cicada orni, Gryllus campestris
        """,
        'usage_tips': """
        ## 💡 Kullanım İpuçları / Usage Tips

        ### 📋 Optimal Kayıt Koşulları / Optimal Recording Conditions:

        **1. Ses Kalitesi / Audio Quality:**
        - **Mikrofon:** Yönlü mikrofon tercih edin
        - **Mesafe:** 1-3 metre arası optimal
        - **Gürültü:** Rüzgar, trafik, insan sesi minimize edin
        - **Format:** WAV (kayıpsız) > MP3 > M4A

        **2. Zaman ve Süre / Timing and Duration:**
        - **Minimum Süre:** 8 saniye (Dolbear yasası gereği)
        - **Optimal Süre:** 15-30 saniye
        - **Zaman:** Akşam saatleri (18:00-22:00) en aktif
        - **Mevsim:** Yaz ayları (Haziran-Eylül)

        **3. Çevresel Faktörler / Environmental Factors:**
        - **Sıcaklık:** 15-35°C arası optimal
        - **Nem:** %40-70 arası
        - **Rüzgar:** <5 km/h
        - **Ortam:** Açık alan, ağaçlık bölge

        ### 🔧 Sistem Ayarları / System Settings:

        **Denoising (Gürültü Temizleme):**
        - ✅ **Açın:** Şehir içi, rüzgarlı ortam
        - ❌ **Kapatın:** Çok sessiz, temiz kayıt

        **Chirp Detection:**
        - ✅ **Her zaman açık** (temel analiz için gerekli)

        **Sıcaklık Yöntemi:**
        - **Temel Dolbear:** Genel kullanım (önerilen)
        - **Cicada orni:** Türkiye'deki yaygın tür için
        - **Fahrenheit:** ABD standardı

        ### ⚠️ Sınırlamalar / Limitations:
        - Tek tür ağustos böceği sesi olmalı
        - Arka plan müziği/konuşma olmamalı
        - Çok yüksek/düşük sıcaklıklarda doğruluk azalır
        - Farklı türler farklı sonuçlar verebilir
        """
    },
    'en': {
        'title': '🦗 Cicada Frequency Analysis System',
        'subtitle': 'Temperature Estimation using Dolbear\'s Law',
        'file_upload_tab': '📁 File Upload',
        'live_record_tab': '🎤 Live Recording',
        'info_tab': 'ℹ️ Information',
        'upload_audio': 'Upload Audio File',
        'upload_desc': 'You can upload WAV, MP3, M4A format audio files',
        'enable_denoising': '🧹 Apply Audio Denoising',
        'enable_chirp': '🔍 Apply Chirp Detection',
        'temp_method': '🌡️ Temperature Calculation Method',
        'analyze_btn': '🚀 Analyze',
        'record_audio': 'Record Audio',
        'record_desc': 'Record live audio from microphone (at least 8 seconds)',
        'status_title': '📊 Analysis Status',
        'result_title': '📈 Results',
        'viz_title': '📊 Graphical Analysis',
        'processed_audio_title': '🎵 Processed Audio',
        'dolbear_info': """
        ## 🦗 About Dolbear's Law

        **History:**
        Dolbear's law is a mathematical relationship between cricket chirp frequency and air temperature, discovered by American physicist Amos Dolbear in 1897.

        ### 📐 Mathematical Formulas:
        - **Basic Dolbear (Celsius):** T(°C) = N₈ + 5
          - N₈: Number of chirps in 8 seconds
        - **Dolbear (Fahrenheit):** T(°F) = N₁₅ + 40
          - N₁₅: Number of chirps in 15 seconds
        - **Cicada orni Special:** T = 1.93 × f + 20.8
          - f: Chirp frequency (Hz)

        ### 🔬 Scientific Basis:
        **Thermoregulation:** Cicadas are cold-blooded (ectothermic) creatures, so their body temperature depends on ambient temperature.

        **Metabolic Relationship:** At higher temperatures:
        - Enzyme activity increases
        - Metabolism accelerates
        - Muscle contraction speed increases
        - Chirp frequency rises

        **Acoustic Mechanism:** Chirp sound is produced by rubbing forewings together. Higher temperature increases muscle contraction speed.

        ### 🎯 Accuracy and Limitations:
        - **Precision:** ±2-3°C under optimal conditions
        - **Temperature Range:** Most accurate between 13-38°C
        - **Species Dependency:** Different coefficients for different species
        - **Environmental Factors:** Humidity, wind, noise effects
        """,
        'system_info': """
        ## ⚙️ System Features

        ### 🧠 AI Models and Algorithms:

        **1. Audio Denoising:**
        - **Model:** Facebook Demucs (DNS48/DNS64/Master64)
        - **Technology:** Deep Neural Network-based noise removal
        - **Purpose:** Isolate cicada sounds by removing environmental noise

        **2. Frequency Analysis:**
        - **Library:** LibROSA (Library for Audio Analysis)
        - **Method:** STFT (Short-Time Fourier Transform)
        - **Features:** Mel spectrogram, MFCC, Spectral centroid
        - **Band-pass Filter:** Butterworth 2-12 kHz (cicada range)

        **3. Chirp Detection:**
        - **Algorithm:** Adaptive threshold with peak detection
        - **Library:** SciPy signal processing
        - **Parameters:** Minimum chirp interval, amplitude threshold

        ### 📊 Technical Specifications:
        - **Sample Rate:** 22.05 kHz (Analysis up to 11 kHz per Nyquist theorem)
        - **Frequency Range:** 2-12 kHz (Cicada orni optimal range)
        - **Analysis Window:** 8 seconds (Dolbear's law standard)
        - **Minimum Recording:** 8 seconds
        - **Supported Formats:** WAV, MP3, M4A
        - **Processing Speed:** <30 seconds (CPU), <10 seconds (GPU)
        - **Platform:** CPU and GPU automatic optimization

        ### 🔬 Scientific Validation:
        - **Reference:** Dolbear, A. E. (1897). "The cricket as a thermometer"
        - **Accuracy:** ±2-3°C (under optimal conditions)
        - **Tested Species:** Cicada orni, Gryllus campestris
        """,
        'usage_tips': """
        ## 💡 Usage Tips

        ### 📋 Optimal Recording Conditions:

        **1. Audio Quality:**
        - **Microphone:** Use directional microphone
        - **Distance:** 1-3 meters optimal
        - **Noise:** Minimize wind, traffic, human voices
        - **Format:** WAV (lossless) > MP3 > M4A

        **2. Timing and Duration:**
        - **Minimum Duration:** 8 seconds (Dolbear's law requirement)
        - **Optimal Duration:** 15-30 seconds
        - **Time:** Evening hours (6:00-10:00 PM) most active
        - **Season:** Summer months (June-September)

        **3. Environmental Factors:**
        - **Temperature:** 15-35°C optimal range
        - **Humidity:** 40-70%
        - **Wind:** <5 km/h
        - **Environment:** Open area, wooded regions

        ### 🔧 System Settings:

        **Denoising (Noise Removal):**
        - ✅ **Enable:** Urban areas, windy conditions
        - ❌ **Disable:** Very quiet, clean recordings

        **Chirp Detection:**
        - ✅ **Always enabled** (required for basic analysis)

        **Temperature Method:**
        - **Basic Dolbear:** General use (recommended)
        - **Cicada orni:** For common Turkish species
        - **Fahrenheit:** US standard

        ### ⚠️ Limitations:
        - Single cicada species sound only
        - No background music/speech
        - Accuracy decreases at extreme temperatures
        - Different species may give different results
        """
    }
}

def get_text(key: str) -> str:
    """UI metni al"""
    return UI_TEXTS[current_language].get(key, key)

def initialize_system():
    """Sistemi başlat"""
    global cicada_filter
    try:
        print("🚀 Sistem başlatılıyor / Initializing system...")
        
        # Ana filtreyi başlat
        cicada_filter = CicadaFilter(sr=22050, denoiser_model='dns48')
        
        print("✅ Sistem başarıyla başlatıldı / System initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Sistem başlatma hatası / System initialization error: {str(e)}")
        return False

def analyze_audio_file(audio_file, enable_denoising, enable_chirp, temp_method, progress=gr.Progress()):
    """
    Ses dosyasını analiz et ve Dolbear yasası ile sıcaklık hesapla

    Dolbear Yasası: Ağustos böcekleri soğukkanlı olduğu için çevre sıcaklığı
    arttıkça metabolizmaları hızlanır ve daha sık chirp yaparlar.
    """
    if audio_file is None:
        return "❌ Lütfen bir ses dosyası yükleyin / Please upload an audio file", "", None, None

    try:
        progress(0.1, desc="Sistem kontrol ediliyor / Checking system...")

        # Sistem kontrolü (tekrar kaldırıldı)
        if cicada_filter is None:
            if not initialize_system():
                return "❌ Sistem başlatılamadı / System could not be initialized", "", None, None

        assert cicada_filter is not None
        progress(0.2, desc="Ses dosyası yükleniyor / Loading audio file...")

        # Analizi başlat - tüm parametreleri geç
        result = cicada_filter.process_audio_file(
            file_path=audio_file,
            enable_denoising=enable_denoising,
            enable_chirp_detection=enable_chirp,
            temperature_method=temp_method
        )

        progress(0.8, desc="Sonuçlar hazırlanıyor / Preparing results...")

        # Sonuçları formatla
        status = cicada_filter.get_result_summary(result, current_language)

        # Detaylı sonuçlar
        detailed_results = format_detailed_results(result)

        # Görselleştirme
        visualization = create_analysis_visualization(result)

        # İşlenmiş ses (şimdilik None)
        processed_audio = None

        progress(1.0, desc="Tamamlandı / Completed")

        return status, detailed_results, visualization, processed_audio

    except Exception as e:
        error_msg = f"❌ Analiz hatası / Analysis error: {str(e)}"
        return error_msg, "", None, None

def analyze_recorded_audio(audio_data, enable_denoising, enable_chirp, temp_method, progress=gr.Progress()):
    """
    Kaydedilen sesi analiz et ve Dolbear yasası ile sıcaklık hesapla

    Gradio mikrofondan gelen audio_data formatı: (sample_rate, audio_array)
    """
    if audio_data is None:
        return "❌ Lütfen ses kaydı yapın / Please record audio", "", None, None

    try:
        progress(0.1, desc="Sistem kontrol ediliyor / Checking system...")

        # Sistem kontrolü (tekrar kaldırıldı)
        if cicada_filter is None:
            if not initialize_system():
                return "❌ Sistem başlatılamadı / System could not be initialized", "", None, None

        assert cicada_filter is not None
        progress(0.2, desc="Ses kaydı işleniyor / Processing audio recording...")

        # Audio verilerini çıkar (Gradio format: (sample_rate, audio_array))
        sample_rate, audio_array = audio_data

        # Stereo'dan mono'ya dönüştür
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)

        # Float'a dönüştür ve normalize et
        if audio_array.dtype != np.float32:
            if audio_array.dtype == np.int16:
                audio_array = audio_array.astype(np.float32) / 32767.0
            elif audio_array.dtype == np.int32:
                audio_array = audio_array.astype(np.float32) / 2147483647.0
            else:
                audio_array = audio_array.astype(np.float32)

        progress(0.4, desc="Analiz başlatılıyor / Starting analysis...")

        # Analizi başlat
        result = cicada_filter.process_audio_array(
            audio=audio_array,
            sr=sample_rate,
            enable_denoising=enable_denoising,
            enable_chirp_detection=enable_chirp,
            temperature_method=temp_method
        )

        progress(0.8, desc="Sonuçlar hazırlanıyor / Preparing results...")

        # Sonuçları formatla
        status = cicada_filter.get_result_summary(result, current_language)

        # Detaylı sonuçlar
        detailed_results = format_detailed_results(result)

        # Görselleştirme
        visualization = create_analysis_visualization(result)

        # İşlenmiş ses
        processed_audio = (sample_rate, audio_array)

        progress(1.0, desc="Tamamlandı / Completed")

        return status, detailed_results, visualization, processed_audio

    except Exception as e:
        error_msg = f"❌ Analiz hatası / Analysis error: {str(e)}"
        return error_msg, "", None, None

def format_detailed_results(result: dict) -> str:
    """Detaylı sonuçları formatla"""
    if not result.get('success', False):
        return f"❌ **Analiz Başarısız / Analysis Failed**\n\n{result.get('error', 'Bilinmeyen hata / Unknown error')}"
    
    try:
        # Ana bilgiler
        summary = result.get('summary', {})
        processing_info = result.get('processing_info', {})
        analysis_results = result.get('analysis_results', {})
        
        # Chirp analizi
        chirp_analysis = analysis_results.get('chirp_analysis', {})
        temperature_calc = analysis_results.get('temperature_calculation', {})
        
        if current_language == 'tr':
            formatted = f"""
## 📋 Detaylı Analiz Sonuçları

### 🎵 Ses Dosyası Bilgileri
- **Süre:** {summary.get('duration_seconds', 0):.1f} saniye
- **Kalite Skoru:** {processing_info.get('quality_assessment', {}).get('quality_score', 0)}/100
- **Denoising:** {"✅ Uygulandı" if processing_info.get('denoising', {}).get('applied', False) else "❌ Uygulanmadı"}

### 🔍 Chirp Analizi
- **Toplam Chirp:** {chirp_analysis.get('total_chirps', 0)}
- **Chirp Oranı:** {chirp_analysis.get('chirp_rate', 0):.2f} chirp/saniye
- **8-Saniyelik Chirp (N₈):** {chirp_analysis.get('window_analysis', {}).get('mean_n8', 0):.1f}
- **Tutarlılık:** {chirp_analysis.get('window_analysis', {}).get('consistency', 0):.1%}

### 🌡️ Sıcaklık Tahmini
- **Tahmini Sıcaklık:** **{temperature_calc.get('temperature', 'N/A')}°C**
- **Güven Seviyesi:** {temperature_calc.get('confidence', 'düşük').title()}
- **Standart Sapma:** ±{temperature_calc.get('std_deviation', 0)}°C
- **Yöntem:** {temperature_calc.get('method_description', 'N/A')}

### 📊 Kalite Değerlendirmesi
"""
        else:
            formatted = f"""
## 📋 Detailed Analysis Results

### 🎵 Audio File Information
- **Duration:** {summary.get('duration_seconds', 0):.1f} seconds
- **Quality Score:** {processing_info.get('quality_assessment', {}).get('quality_score', 0)}/100
- **Denoising:** {"✅ Applied" if processing_info.get('denoising', {}).get('applied', False) else "❌ Not Applied"}

### 🔍 Chirp Analysis
- **Total Chirps:** {chirp_analysis.get('total_chirps', 0)}
- **Chirp Rate:** {chirp_analysis.get('chirp_rate', 0):.2f} chirps/second
- **8-Second Chirps (N₈):** {chirp_analysis.get('window_analysis', {}).get('mean_n8', 0):.1f}
- **Consistency:** {chirp_analysis.get('window_analysis', {}).get('consistency', 0):.1%}

### 🌡️ Temperature Estimation
- **Estimated Temperature:** **{temperature_calc.get('temperature', 'N/A')}°C**
- **Confidence Level:** {temperature_calc.get('confidence', 'low').title()}
- **Standard Deviation:** ±{temperature_calc.get('std_deviation', 0)}°C
- **Method:** {temperature_calc.get('method_description', 'N/A')}

### 📊 Quality Assessment
"""
        
        # Kalite uyarıları ekle
        quality_warnings = processing_info.get('quality_assessment', {}).get('warnings', [])
        for warning in quality_warnings:
            formatted += f"- {warning}\n"
        
        return formatted.strip()
        
    except Exception as e:
        return f"❌ Sonuç formatlanırken hata / Error formatting results: {str(e)}"

def create_analysis_visualization(result: dict):
    """
    Bilimsel analiz görselleştirmesi oluştur

    Dolbear Yasası temelinde chirp analizi ve sıcaklık tahmini görselleştirmesi:
    1. Chirp Detection Timeline - Zaman içinde tespit edilen chirp'ler
    2. Temperature Estimation - Güven seviyesi ile sıcaklık tahmini
    3. Frequency Analysis - Ağustos böceği frekans aralığı analizi
    """
    if not result.get('success', False):
        return None

    try:
        # 3 panel grafik oluştur
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 10))

        # Panel 1: Chirp Detection Timeline
        chirp_analysis = result.get('analysis_results', {}).get('chirp_analysis', {})
        chirp_times = chirp_analysis.get('chirp_times', [])
        total_chirps = chirp_analysis.get('total_chirps', 0)

        if chirp_times:
            ax1.scatter(chirp_times, [1]*len(chirp_times), alpha=0.8, s=60, c='red', marker='|')
            ax1.set_ylabel('Chirp Events')
            ax1.set_title(f'🔍 Chirp Detection Timeline (Total: {total_chirps} chirps)')
            ax1.grid(True, alpha=0.3)
            ax1.set_ylim(0.5, 1.5)
        else:
            ax1.text(0.5, 0.5, 'Chirp tespit edilemedi\nNo chirps detected',
                    ha='center', va='center', transform=ax1.transAxes, fontsize=12)
            ax1.set_title('🔍 Chirp Detection Timeline')

        # Panel 2: Temperature Estimation with Dolbear's Law
        temp_calc = result.get('analysis_results', {}).get('temperature_calculation', {})
        temperature = temp_calc.get('temperature', None)
        confidence = temp_calc.get('confidence', 'low')
        method = temp_calc.get('method', 'unknown')

        if temperature is not None:
            colors = {'high': '#2E8B57', 'medium': '#FF8C00', 'low': '#DC143C'}
            color = colors.get(confidence, 'gray')

            bars = ax2.bar(['Estimated\nTemperature'], [temperature],
                          color=color, alpha=0.7, width=0.5)
            ax2.set_ylabel('Temperature (°C)')
            ax2.set_title(f'🌡️ Dolbear\'s Law Temperature Estimation\n({confidence.title()} Confidence - {method})')
            ax2.grid(True, alpha=0.3)

            # Sıcaklık değerini bar üzerine yaz
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{temperature:.1f}°C', ha='center', va='bottom', fontweight='bold')
        else:
            ax2.text(0.5, 0.5, 'Sıcaklık hesaplanamadı\nTemperature could not be calculated',
                    ha='center', va='center', transform=ax2.transAxes, fontsize=12)
            ax2.set_title('🌡️ Dolbear\'s Law Temperature Estimation')

        # Panel 3: Frequency Analysis
        freq_analysis = result.get('analysis_results', {}).get('frequency_analysis', {})
        if freq_analysis.get('success', False):
            # Basit frekans dağılımı gösterimi (gerçek implementasyonda spektrogram olacak)
            ax3.axvspan(2000, 12000, alpha=0.3, color='green', label='Cicada Range (2-12 kHz)')
            ax3.set_xlabel('Frequency (Hz)')
            ax3.set_ylabel('Energy')
            ax3.set_title('📊 Frequency Analysis - Cicada Range (2-12 kHz)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            ax3.set_xlim(0, 15000)
        else:
            ax3.text(0.5, 0.5, 'Frekans analizi başarısız\nFrequency analysis failed',
                    ha='center', va='center', transform=ax3.transAxes, fontsize=12)
            ax3.set_title('📊 Frequency Analysis')

        plt.tight_layout()
        return fig

    except Exception as e:
        print(f"Görselleştirme hatası / Visualization error: {str(e)}")
        return None

def change_language():
    """Dil değiştir"""
    global current_language
    current_language = 'en' if current_language == 'tr' else 'tr'
    return get_text('title')

def create_gradio_interface():
    """
    Bilimsel Gradio arayüzünü oluştur

    Dolbear yasası temelinde ağustos böceği analizi için
    kullanıcı dostu web tabanlı arayüz
    """

    with gr.Blocks(
        title="🦗 Cicada Analysis",
        css=".gradio-container { background: white !important; max-width: 1200px; margin: auto; }",
        theme="default"
    ) as app:
        
        # Başlık
        gr.Markdown(f"""
        # {get_text('title')}
        ## {get_text('subtitle')}
        
        ---
        """)
        
        # Dil değiştirme butonu
        with gr.Row():
            with gr.Column(scale=1):
                lang_btn = gr.Button("🌐 TR/EN")
            with gr.Column(scale=4):
                title_display = gr.Markdown(get_text('title'))
        
        # Ana sekmeler
        with gr.Tabs():
            
            # Dosya yükleme sekmesi
            with gr.TabItem(get_text('file_upload_tab')):
                with gr.Row():
                    with gr.Column(scale=1):
                        # Girdi kontrolleri
                        audio_input = gr.Audio(
                            label=get_text('upload_audio'),
                            type="filepath",
                            sources=["upload"]
                        )
                        
                        gr.Markdown(get_text('upload_desc'))
                        
                        with gr.Row():
                            enable_denoising = gr.Checkbox(
                                label=get_text('enable_denoising'), 
                                value=True
                            )
                            enable_chirp = gr.Checkbox(
                                label=get_text('enable_chirp'), 
                                value=True
                            )
                        
                        temp_method = gr.Dropdown(
                            choices=[
                                ("Temel Dolbear (Celsius)", "basic_celsius"),
                                ("Cicada orni Özel", "cicada_orni_special"),
                                ("Dolbear (Fahrenheit)", "dolbear_fahrenheit")
                            ],
                            value="basic_celsius",
                            label=get_text('temp_method')
                        )
                        
                        analyze_btn = gr.Button(
                            get_text('analyze_btn'), 
                            variant="primary",
                            size="lg"
                        )
                    
                    with gr.Column(scale=2):
                        # Çıktılar
                        status_output = gr.Markdown(
                            label=get_text('status_title')
                        )
                        
                        detailed_output = gr.Markdown(
                            label=get_text('result_title')
                        )
                        
                        visualization_output = gr.Plot(
                            label=get_text('viz_title')
                        )
                        
                        processed_audio_output = gr.Audio(
                            label=get_text('processed_audio_title'),
                            visible=False
                        )
                
                # Event handlers
                analyze_btn.click(
                    fn=analyze_audio_file,
                    inputs=[audio_input, enable_denoising, enable_chirp, temp_method],
                    outputs=[status_output, detailed_output, visualization_output, processed_audio_output]
                )
            
            # Canlı kayıt sekmesi
            with gr.TabItem(get_text('live_record_tab')):
                with gr.Row():
                    with gr.Column(scale=1):
                        # Kayıt kontrolleri
                        audio_record = gr.Audio(
                            label=get_text('record_audio'),
                            type="numpy",
                            sources=["microphone"]
                        )
                        
                        gr.Markdown(get_text('record_desc'))
                        
                        with gr.Row():
                            enable_denoising_rec = gr.Checkbox(
                                label=get_text('enable_denoising'), 
                                value=True
                            )
                            enable_chirp_rec = gr.Checkbox(
                                label=get_text('enable_chirp'), 
                                value=True
                            )
                        
                        temp_method_rec = gr.Dropdown(
                            choices=[
                                ("Temel Dolbear (Celsius)", "basic_celsius"),
                                ("Cicada orni Özel", "cicada_orni_special"),
                                ("Dolbear (Fahrenheit)", "dolbear_fahrenheit")
                            ],
                            value="basic_celsius",
                            label=get_text('temp_method')
                        )
                        
                        analyze_rec_btn = gr.Button(
                            get_text('analyze_btn'), 
                            variant="primary",
                            size="lg"
                        )
                    
                    with gr.Column(scale=2):
                        # Çıktılar
                        status_output_rec = gr.Markdown(
                            label=get_text('status_title')
                        )
                        
                        detailed_output_rec = gr.Markdown(
                            label=get_text('result_title')
                        )
                        
                        visualization_output_rec = gr.Plot(
                            label=get_text('viz_title')
                        )
                        
                        processed_audio_output_rec = gr.Audio(
                            label=get_text('processed_audio_title'),
                            visible=False
                        )
                
                # Event handlers
                analyze_rec_btn.click(
                    fn=analyze_recorded_audio,
                    inputs=[audio_record, enable_denoising_rec, enable_chirp_rec, temp_method_rec],
                    outputs=[status_output_rec, detailed_output_rec, visualization_output_rec, processed_audio_output_rec]
                )
            
            # Bilgi sekmesi
            with gr.TabItem(get_text('info_tab')):
                with gr.Tabs():
                    with gr.TabItem("📐 Dolbear Yasası"):
                        gr.Markdown(get_text('dolbear_info'))
                    
                    with gr.TabItem("⚙️ Sistem"):
                        gr.Markdown(get_text('system_info'))
                    
                    with gr.TabItem("💡 İpuçları"):
                        gr.Markdown(get_text('usage_tips'))
        
        # Dil değiştirme eventi
        lang_btn.click(
            fn=change_language,
            outputs=[title_display]
        )
        
        # Footer
        gr.Markdown("""
        ---
        **Geliştirici / Developer:** Sems Kurtoglu | **GitHub:** [semskurto/cicada-thermometer](https://github.com/semskurto/cicada-thermometer)
        """)
    
    return app

def main():
    """Ana fonksiyon"""
    print("🦗 Ağustos Böceği Frekans Analizi Sistemi başlatılıyor...")
    print("🦗 Cicada Frequency Analysis System starting...")
    
    # Sistemi başlat
    if initialize_system():
        print("✅ Sistem hazır / System ready")
    else:
        print("⚠️ Sistem kısmen hazır (bazı özellikler devre dışı) / System partially ready (some features disabled)")
    
    # Gradio arayüzünü oluştur ve başlat
    app = create_gradio_interface()
    
    print("🌐 Web arayüzü başlatılıyor / Starting web interface...")
    
    app.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=True,
        debug=True,
        show_error=True,
        inbrowser=False,  # Manuel açma
        quiet=False
    )

if __name__ == "__main__":
    main()
