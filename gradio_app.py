"""
🦗 Ağustos Böceği Frekans Analizi Sistemi - Gradio Interface
Cicada Frequency Analysis System - Gradio Interface

Bu modül, ağ<PERSON><PERSON> bö<PERSON>ği seslerinden frekans analizi yaparak Dolbear yasası ile 
hava sıcaklığı tahmin eden web tabanlı kullanıcı arayüzünü sağlar.

Author: Sems Kurtoglu
"""

import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

from cicada_analyzer import CicadaFilter
from cicada_analyzer.models.denoiser_model import DenoiserModel
from cicada_analyzer.utils.temperature_calc import TemperatureCalculator

# Global değişkenler
cicada_filter = None
current_language = 'tr'

# UI Metinleri
UI_TEXTS = {
    'tr': {
        'title': '🦗 Ağustos Böceği Frekans Analizi Sistemi',
        'subtitle': 'Dolbear Yasası ile Hava Sıcaklığı Tahmini',
        'file_upload_tab': '📁 Do<PERSON>a Yükleme',
        'live_record_tab': '🎤 Canlı Kayıt',
        'info_tab': 'ℹ️ Bilgi',
        'upload_audio': 'Ses Dosyası Yükleyin',
        'upload_desc': 'WAV, MP3, M4A formatlarında ses dosyası yükleyebilirsiniz',
        'enable_denoising': '🧹 Ses Temizleme (Denoising) Uygula',
        'enable_chirp': '🔍 Chirp Tespiti Uygula',
        'temp_method': '🌡️ Sıcaklık Hesaplama Yöntemi',
        'analyze_btn': '🚀 Analiz Et',
        'record_audio': 'Ses Kaydet',
        'record_desc': 'Mikrofondan canlı ses kaydı yapın (en az 8 saniye)',
        'status_title': '📊 Analiz Durumu',
        'result_title': '📈 Sonuçlar',
        'viz_title': '📊 Grafik Analizi',
        'processed_audio_title': '🎵 İşlenmiş Ses',
        'dolbear_info': """
        ## 🦗 Dolbear Yasası Hakkında
        
        Dolbear yasası, 1897'de Amos Dolbear tarafından keşfedilen, ağustos böceği chirp frekansı ile hava sıcaklığı arasındaki matematiksel ilişkiyi açıklayan yasadır.
        
        ### 📐 Formüller:
        - **Temel Dolbear (Celsius):** T(°C) = N₈ + 5
        - **Dolbear (Fahrenheit):** T(°F) = N₁₅ + 40  
        - **Cicada orni Özel:** T = 1.93 × f + 20.8
        
        ### 🔬 Bilimsel Temel:
        Ağustos böcekleri soğukkanlı canlılar olduğu için, vücut sıcaklıkları çevre sıcaklığına bağlıdır. Yüksek sıcaklıklarda metabolizmaları hızlanır ve daha sık chirp yaparlar.
        
        ### 🎯 Doğruluk:
        Uygun koşullarda ±3°C hassasiyetle sıcaklık tahmini yapılabilir.
        """,
        'system_info': """
        ## ⚙️ Sistem Özellikleri
        
        ### 🧠 AI Modelleri:
        - **Denoising:** Facebook Demucs (DNS48/DNS64)
        - **Frekans Analizi:** LibROSA tabanlı
        - **Chirp Detection:** Adaptive threshold algoritması
        
        ### 📊 Teknik Özellikler:
        - Sample Rate: 22.05 kHz
        - Frekans Aralığı: 2-12 kHz (ağustos böceği)
        - Analiz Penceresi: 8 saniye (Dolbear yasası)
        - GPU/CPU otomatik optimizasyon
        """,
        'usage_tips': """
        ## 💡 Kullanım İpuçları
        
        ### 📋 En İyi Sonuçlar İçin:
        1. **Ses Kalitesi:** Temiz, gürültüsüz kayıt
        2. **Süre:** En az 8 saniye kayıt
        3. **Çevre:** Sessiz ortam
        4. **Mesafe:** Ağustos böceğine yakın kayıt
        
        ### 🔧 Ayarlar:
        - **Denoising:** Gürültülü ortamlarda açın
        - **Chirp Detection:** Her zaman açık tutun
        - **Yöntem:** "Temel Dolbear" önerilir
        """
    },
    'en': {
        'title': '🦗 Cicada Frequency Analysis System',
        'subtitle': 'Temperature Estimation using Dolbear\'s Law',
        'file_upload_tab': '📁 File Upload',
        'live_record_tab': '🎤 Live Recording',
        'info_tab': 'ℹ️ Information',
        'upload_audio': 'Upload Audio File',
        'upload_desc': 'You can upload WAV, MP3, M4A format audio files',
        'enable_denoising': '🧹 Apply Audio Denoising',
        'enable_chirp': '🔍 Apply Chirp Detection',
        'temp_method': '🌡️ Temperature Calculation Method',
        'analyze_btn': '🚀 Analyze',
        'record_audio': 'Record Audio',
        'record_desc': 'Record live audio from microphone (at least 8 seconds)',
        'status_title': '📊 Analysis Status',
        'result_title': '📈 Results',
        'viz_title': '📊 Graphical Analysis',
        'processed_audio_title': '🎵 Processed Audio',
        'dolbear_info': """
        ## 🦗 About Dolbear's Law
        
        Dolbear's law is a mathematical relationship between cricket chirp frequency and air temperature, discovered by Amos Dolbear in 1897.
        
        ### 📐 Formulas:
        - **Basic Dolbear (Celsius):** T(°C) = N₈ + 5
        - **Dolbear (Fahrenheit):** T(°F) = N₁₅ + 40
        - **Cicada orni Special:** T = 1.93 × f + 20.8
        
        ### 🔬 Scientific Basis:
        Cicadas are cold-blooded creatures, so their body temperature depends on ambient temperature. At higher temperatures, their metabolism accelerates and they chirp more frequently.
        
        ### 🎯 Accuracy:
        Under suitable conditions, temperature estimation can be made with ±3°C precision.
        """,
        'system_info': """
        ## ⚙️ System Features
        
        ### 🧠 AI Models:
        - **Denoising:** Facebook Demucs (DNS48/DNS64)
        - **Frequency Analysis:** LibROSA-based
        - **Chirp Detection:** Adaptive threshold algorithm
        
        ### 📊 Technical Specifications:
        - Sample Rate: 22.05 kHz
        - Frequency Range: 2-12 kHz (cicada range)
        - Analysis Window: 8 seconds (Dolbear's law)
        - GPU/CPU automatic optimization
        """,
        'usage_tips': """
        ## 💡 Usage Tips
        
        ### 📋 For Best Results:
        1. **Audio Quality:** Clean, noise-free recording
        2. **Duration:** At least 8 seconds recording
        3. **Environment:** Quiet surroundings
        4. **Distance:** Close recording to cicada
        
        ### 🔧 Settings:
        - **Denoising:** Enable in noisy environments
        - **Chirp Detection:** Keep always enabled
        - **Method:** "Basic Dolbear" recommended
        """
    }
}

def get_text(key: str) -> str:
    """UI metni al"""
    return UI_TEXTS[current_language].get(key, key)

def initialize_system():
    """Sistemi başlat"""
    global cicada_filter
    try:
        print("🚀 Sistem başlatılıyor / Initializing system...")
        
        # Ana filtreyi başlat
        cicada_filter = CicadaFilter(sr=22050, denoiser_model='dns48')
        
        print("✅ Sistem başarıyla başlatıldı / System initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Sistem başlatma hatası / System initialization error: {str(e)}")
        return False

def analyze_audio_file(audio_file, enable_denoising, enable_chirp, temp_method, progress=gr.Progress()):
    """Ses dosyasını analiz et"""
    if audio_file is None:
        return "❌ Lütfen bir ses dosyası yükleyin / Please upload an audio file", "", None, None
    
    try:
        progress(0.1, desc="Sistem kontrol ediliyor / Checking system...")
        
        if cicada_filter is None:
            if not initialize_system():
                return "❌ Sistem başlatılamadı / System could not be initialized", "", None, None
        
        progress(0.2, desc="Ses dosyası yükleniyor / Loading audio file...")
        
        # Analizi başlat
        result = cicada_filter.process_audio_file(
            file_path=audio_file,
            enable_denoising=enable_denoising,
            enable_chirp_detection=enable_chirp,
            temperature_method=temp_method
        )
        
        progress(0.8, desc="Sonuçlar hazırlanıyor / Preparing results...")
        
        # Sonuçları formatla
        status = cicada_filter.get_result_summary(result, current_language)
        
        # Detaylı sonuçlar
        detailed_results = format_detailed_results(result)
        
        # Görselleştirme (placeholder)
        visualization = create_placeholder_visualization(result)
        
        # İşlenmiş ses (placeholder)
        processed_audio = None  # Gerçek implementasyonda işlenmiş ses döndürülür
        
        progress(1.0, desc="Tamamlandı / Completed")
        
        return status, detailed_results, visualization, processed_audio
        
    except Exception as e:
        error_msg = f"❌ Analiz hatası / Analysis error: {str(e)}"
        return error_msg, "", None, None

def analyze_recorded_audio(audio_data, enable_denoising, enable_chirp, temp_method, progress=gr.Progress()):
    """Kaydedilen sesi analiz et"""
    if audio_data is None:
        return "❌ Lütfen ses kaydı yapın / Please record audio", "", None, None
    
    try:
        progress(0.1, desc="Sistem kontrol ediliyor / Checking system...")
        
        if cicada_filter is None:
            if not initialize_system():
                return "❌ Sistem başlatılamadı / System could not be initialized", "", None, None
        
        progress(0.2, desc="Ses kaydı işleniyor / Processing audio recording...")
        
        # Audio verilerini çıkar
        sample_rate, audio_array = audio_data
        
        # Stereo'dan mono'ya dönüştür
        if len(audio_array.shape) > 1:
            audio_array = np.mean(audio_array, axis=1)
        
        # Float'a dönüştür ve normalize et
        if audio_array.dtype != np.float32:
            audio_array = audio_array.astype(np.float32)
            if audio_array.dtype == np.int16:
                audio_array = audio_array / 32767.0
            elif audio_array.dtype == np.int32:
                audio_array = audio_array / 2147483647.0
        
        progress(0.4, desc="Analiz başlatılıyor / Starting analysis...")
        
        # Analizi başlat
        result = cicada_filter.process_audio_array(
            audio=audio_array,
            sr=sample_rate,
            enable_denoising=enable_denoising,
            enable_chirp_detection=enable_chirp,
            temperature_method=temp_method
        )
        
        progress(0.8, desc="Sonuçlar hazırlanıyor / Preparing results...")
        
        # Sonuçları formatla
        status = cicada_filter.get_result_summary(result, current_language)
        
        # Detaylı sonuçlar
        detailed_results = format_detailed_results(result)
        
        # Görselleştirme
        visualization = create_placeholder_visualization(result)
        
        # İşlenmiş ses
        processed_audio = (sample_rate, audio_array)  # Placeholder
        
        progress(1.0, desc="Tamamlandı / Completed")
        
        return status, detailed_results, visualization, processed_audio
        
    except Exception as e:
        error_msg = f"❌ Analiz hatası / Analysis error: {str(e)}"
        return error_msg, "", None, None

def format_detailed_results(result: dict) -> str:
    """Detaylı sonuçları formatla"""
    if not result.get('success', False):
        return f"❌ **Analiz Başarısız / Analysis Failed**\n\n{result.get('error', 'Bilinmeyen hata / Unknown error')}"
    
    try:
        # Ana bilgiler
        summary = result.get('summary', {})
        processing_info = result.get('processing_info', {})
        analysis_results = result.get('analysis_results', {})
        
        # Chirp analizi
        chirp_analysis = analysis_results.get('chirp_analysis', {})
        temperature_calc = analysis_results.get('temperature_calculation', {})
        
        if current_language == 'tr':
            formatted = f"""
## 📋 Detaylı Analiz Sonuçları

### 🎵 Ses Dosyası Bilgileri
- **Süre:** {summary.get('duration_seconds', 0):.1f} saniye
- **Kalite Skoru:** {processing_info.get('quality_assessment', {}).get('quality_score', 0)}/100
- **Denoising:** {"✅ Uygulandı" if processing_info.get('denoising', {}).get('applied', False) else "❌ Uygulanmadı"}

### 🔍 Chirp Analizi
- **Toplam Chirp:** {chirp_analysis.get('total_chirps', 0)}
- **Chirp Oranı:** {chirp_analysis.get('chirp_rate', 0):.2f} chirp/saniye
- **8-Saniyelik Chirp (N₈):** {chirp_analysis.get('window_analysis', {}).get('mean_n8', 0):.1f}
- **Tutarlılık:** {chirp_analysis.get('window_analysis', {}).get('consistency', 0):.1%}

### 🌡️ Sıcaklık Tahmini
- **Tahmini Sıcaklık:** **{temperature_calc.get('temperature', 'N/A')}°C**
- **Güven Seviyesi:** {temperature_calc.get('confidence', 'düşük').title()}
- **Standart Sapma:** ±{temperature_calc.get('std_deviation', 0)}°C
- **Yöntem:** {temperature_calc.get('method_description', 'N/A')}

### 📊 Kalite Değerlendirmesi
"""
        else:
            formatted = f"""
## 📋 Detailed Analysis Results

### 🎵 Audio File Information
- **Duration:** {summary.get('duration_seconds', 0):.1f} seconds
- **Quality Score:** {processing_info.get('quality_assessment', {}).get('quality_score', 0)}/100
- **Denoising:** {"✅ Applied" if processing_info.get('denoising', {}).get('applied', False) else "❌ Not Applied"}

### 🔍 Chirp Analysis
- **Total Chirps:** {chirp_analysis.get('total_chirps', 0)}
- **Chirp Rate:** {chirp_analysis.get('chirp_rate', 0):.2f} chirps/second
- **8-Second Chirps (N₈):** {chirp_analysis.get('window_analysis', {}).get('mean_n8', 0):.1f}
- **Consistency:** {chirp_analysis.get('window_analysis', {}).get('consistency', 0):.1%}

### 🌡️ Temperature Estimation
- **Estimated Temperature:** **{temperature_calc.get('temperature', 'N/A')}°C**
- **Confidence Level:** {temperature_calc.get('confidence', 'low').title()}
- **Standard Deviation:** ±{temperature_calc.get('std_deviation', 0)}°C
- **Method:** {temperature_calc.get('method_description', 'N/A')}

### 📊 Quality Assessment
"""
        
        # Kalite uyarıları ekle
        quality_warnings = processing_info.get('quality_assessment', {}).get('warnings', [])
        for warning in quality_warnings:
            formatted += f"- {warning}\n"
        
        return formatted.strip()
        
    except Exception as e:
        return f"❌ Sonuç formatlanırken hata / Error formatting results: {str(e)}"

def create_placeholder_visualization(result: dict):
    """Placeholder görselleştirme oluştur"""
    if not result.get('success', False):
        return None
    
    try:
        # Basit bir örnek grafik oluştur
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
        
        # Chirp analizi grafiği
        chirp_analysis = result.get('analysis_results', {}).get('chirp_analysis', {})
        chirp_times = chirp_analysis.get('chirp_times', [])
        
        if chirp_times:
            ax1.scatter(chirp_times, [1]*len(chirp_times), alpha=0.7, s=50)
            ax1.set_ylabel('Chirp Events')
            ax1.set_title('🔍 Tespit Edilen Chirp\'ler / Detected Chirps')
            ax1.grid(True, alpha=0.3)
        else:
            ax1.text(0.5, 0.5, 'Chirp tespit edilemedi\nNo chirps detected', 
                    ha='center', va='center', transform=ax1.transAxes)
        
        # Sıcaklık sonucu
        temp_calc = result.get('analysis_results', {}).get('temperature_calculation', {})
        temperature = temp_calc.get('temperature', None)
        confidence = temp_calc.get('confidence', 'low')
        
        if temperature is not None:
            colors = {'high': 'green', 'medium': 'orange', 'low': 'red'}
            color = colors.get(confidence, 'gray')
            
            ax2.bar(['Tahmini Sıcaklık\nEstimated Temperature'], [temperature], 
                   color=color, alpha=0.7)
            ax2.set_ylabel('°C')
            ax2.set_title(f'🌡️ Sıcaklık Tahmini / Temperature Estimation ({confidence.title()} Confidence)')
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, 'Sıcaklık hesaplanamadı\nTemperature could not be calculated', 
                    ha='center', va='center', transform=ax2.transAxes)
        
        plt.tight_layout()
        return fig
        
    except Exception as e:
        print(f"Görselleştirme hatası / Visualization error: {str(e)}")
        return None

def change_language():
    """Dil değiştir"""
    global current_language
    current_language = 'en' if current_language == 'tr' else 'tr'
    return get_text('title')

def create_gradio_interface():
    """Gradio arayüzünü oluştur"""
    
    # Basit CSS - sorun çıkarabilecek CSS'i kaldır
    css = ""
    
    # Basit tema
    theme = gr.themes.Base()
    
    with gr.Blocks(title="🦗 Cicada Analysis", theme=theme) as app:
        
        # Başlık
        gr.Markdown(f"""
        # {get_text('title')}
        ## {get_text('subtitle')}
        
        ---
        """)
        
        # Dil değiştirme butonu
        with gr.Row():
            with gr.Column(scale=1):
                lang_btn = gr.Button("🌐 TR/EN")
            with gr.Column(scale=4):
                title_display = gr.Markdown(get_text('title'))
        
        # Ana sekmeler
        with gr.Tabs():
            
            # Dosya yükleme sekmesi
            with gr.TabItem(get_text('file_upload_tab')):
                with gr.Row():
                    with gr.Column(scale=1):
                        # Girdi kontrolleri
                        audio_input = gr.Audio(
                            label=get_text('upload_audio'),
                            type="filepath",
                            sources=["upload"]
                        )
                        
                        gr.Markdown(get_text('upload_desc'))
                        
                        with gr.Row():
                            enable_denoising = gr.Checkbox(
                                label=get_text('enable_denoising'), 
                                value=True
                            )
                            enable_chirp = gr.Checkbox(
                                label=get_text('enable_chirp'), 
                                value=True
                            )
                        
                        temp_method = gr.Dropdown(
                            choices=[
                                ("Temel Dolbear (Celsius)", "basic_celsius"),
                                ("Cicada orni Özel", "cicada_orni_special"),
                                ("Dolbear (Fahrenheit)", "dolbear_fahrenheit")
                            ],
                            value="basic_celsius",
                            label=get_text('temp_method')
                        )
                        
                        analyze_btn = gr.Button(
                            get_text('analyze_btn'), 
                            variant="primary",
                            size="lg"
                        )
                    
                    with gr.Column(scale=2):
                        # Çıktılar
                        status_output = gr.Markdown(
                            label=get_text('status_title')
                        )
                        
                        detailed_output = gr.Markdown(
                            label=get_text('result_title')
                        )
                        
                        visualization_output = gr.Plot(
                            label=get_text('viz_title')
                        )
                        
                        processed_audio_output = gr.Audio(
                            label=get_text('processed_audio_title'),
                            visible=False
                        )
                
                # Event handlers
                analyze_btn.click(
                    fn=analyze_audio_file,
                    inputs=[audio_input, enable_denoising, enable_chirp, temp_method],
                    outputs=[status_output, detailed_output, visualization_output, processed_audio_output]
                )
            
            # Canlı kayıt sekmesi
            with gr.TabItem(get_text('live_record_tab')):
                with gr.Row():
                    with gr.Column(scale=1):
                        # Kayıt kontrolleri
                        audio_record = gr.Audio(
                            label=get_text('record_audio'),
                            type="numpy",
                            sources=["microphone"]
                        )
                        
                        gr.Markdown(get_text('record_desc'))
                        
                        with gr.Row():
                            enable_denoising_rec = gr.Checkbox(
                                label=get_text('enable_denoising'), 
                                value=True
                            )
                            enable_chirp_rec = gr.Checkbox(
                                label=get_text('enable_chirp'), 
                                value=True
                            )
                        
                        temp_method_rec = gr.Dropdown(
                            choices=[
                                ("Temel Dolbear (Celsius)", "basic_celsius"),
                                ("Cicada orni Özel", "cicada_orni_special"),
                                ("Dolbear (Fahrenheit)", "dolbear_fahrenheit")
                            ],
                            value="basic_celsius",
                            label=get_text('temp_method')
                        )
                        
                        analyze_rec_btn = gr.Button(
                            get_text('analyze_btn'), 
                            variant="primary",
                            size="lg"
                        )
                    
                    with gr.Column(scale=2):
                        # Çıktılar
                        status_output_rec = gr.Markdown(
                            label=get_text('status_title')
                        )
                        
                        detailed_output_rec = gr.Markdown(
                            label=get_text('result_title')
                        )
                        
                        visualization_output_rec = gr.Plot(
                            label=get_text('viz_title')
                        )
                        
                        processed_audio_output_rec = gr.Audio(
                            label=get_text('processed_audio_title'),
                            visible=False
                        )
                
                # Event handlers
                analyze_rec_btn.click(
                    fn=analyze_recorded_audio,
                    inputs=[audio_record, enable_denoising_rec, enable_chirp_rec, temp_method_rec],
                    outputs=[status_output_rec, detailed_output_rec, visualization_output_rec, processed_audio_output_rec]
                )
            
            # Bilgi sekmesi
            with gr.TabItem(get_text('info_tab')):
                with gr.Tabs():
                    with gr.TabItem("📐 Dolbear Yasası"):
                        gr.Markdown(get_text('dolbear_info'))
                    
                    with gr.TabItem("⚙️ Sistem"):
                        gr.Markdown(get_text('system_info'))
                    
                    with gr.TabItem("💡 İpuçları"):
                        gr.Markdown(get_text('usage_tips'))
        
        # Dil değiştirme eventi
        lang_btn.click(
            fn=change_language,
            outputs=[title_display]
        )
        
        # Footer
        gr.Markdown("""
        ---
        **Geliştirici / Developer:** Sems Kurtoglu | **GitHub:** [semskurto/cicada-thermometer](https://github.com/semskurto/cicada-thermometer)
        """)
    
    return app

def main():
    """Ana fonksiyon"""
    print("🦗 Ağustos Böceği Frekans Analizi Sistemi başlatılıyor...")
    print("🦗 Cicada Frequency Analysis System starting...")
    
    # Sistemi başlat
    if initialize_system():
        print("✅ Sistem hazır / System ready")
    else:
        print("⚠️ Sistem kısmen hazır (bazı özellikler devre dışı) / System partially ready (some features disabled)")
    
    # Gradio arayüzünü oluştur ve başlat
    app = create_gradio_interface()
    
    print("🌐 Web arayüzü başlatılıyor / Starting web interface...")
    
    app.launch(
        server_name="127.0.0.1",  # Localhost
        server_port=7860,         # Port
        share=False,              # Public link oluşturma
        debug=True,               # Debug modu
        show_error=True,          # Hata gösterimi
        inbrowser=True,           # Tarayıcıda otomatik aç
        prevent_thread_lock=False,
        quiet=False
    )

if __name__ == "__main__":
    main()
