"""
Audio Utilities - Ses dosyası işlemleri / Audio file operations

Bu modül ses dosyalarının yü<PERSON>, d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve temel işlemleri için 
yardımcı fonksiyonlar içerir.

Author: <PERSON><PERSON>lu
"""

import numpy as np
import librosa
import soundfile as sf
import torch
from typing import Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

class AudioUtils:
    """Audio dosyası işlemleri için yardımcı sınıf"""
    
    # Audio konfigürasyon ayarları
    AUDIO_CONFIG = {
        'target_sr': 22050,
        'n_fft': 2048,
        'hop_length': 512,
        'window_size': 8,  # Dolbear yasası için 8 saniye
        'cicada_freq_range': (2000, 12000),
        'highpass_cutoff': 1000
    }
    
    @staticmethod
    def load_audio(file_path: str, target_sr: int = None) -> Tuple[np.ndarray, int]:
        """
        Audio dosyasını yükle ve ön işleme yap
        
        Args:
            file_path: Audio dosya yolu
            target_sr: Hedef sample rate (None ise varsayılan kullanılır)
            
        Returns:
            Tuple[audio_data, sample_rate]
        """
        target_sr = target_sr or AudioUtils.AUDIO_CONFIG['target_sr']
        
        try:
            # LibROSA ile audio yükleme
            audio, sr = librosa.load(file_path, sr=target_sr, mono=True)
            
            # Audio normalizasyonu
            audio = AudioUtils.normalize_audio(audio)
            
            return audio, sr
            
        except Exception as e:
            raise ValueError(f"Audio dosyası yüklenemedi / Could not load audio file: {str(e)}")
    
    @staticmethod
    def normalize_audio(audio: np.ndarray, method: str = 'peak') -> np.ndarray:
        """
        Audio sinyal normalizasyonu
        
        Args:
            audio: Audio sinyal
            method: Normalizasyon yöntemi ('peak', 'rms')
            
        Returns:
            Normalize edilmiş audio sinyal
        """
        if len(audio) == 0:
            return audio
            
        if method == 'peak':
            # Peak normalizasyon
            max_val = np.max(np.abs(audio))
            if max_val > 0:
                audio = audio / max_val
        elif method == 'rms':
            # RMS normalizasyon
            rms = np.sqrt(np.mean(audio**2))
            if rms > 0:
                audio = audio / rms * 0.1  # RMS'i 0.1'e normalize et
                
        return audio
    
    @staticmethod
    def get_audio_info(audio: np.ndarray, sr: int) -> dict:
        """
        Audio dosyası hakkında bilgi al
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            
        Returns:
            Audio bilgileri dictionary
        """
        duration = len(audio) / sr
        rms_energy = np.sqrt(np.mean(audio**2))
        
        # Zero crossing rate
        zcr = librosa.feature.zero_crossing_rate(audio)[0]
        mean_zcr = np.mean(zcr)
        
        # Spektral merkez
        spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
        mean_spectral_centroid = np.mean(spectral_centroids)
        
        return {
            'duration': duration,
            'sample_rate': sr,
            'rms_energy': rms_energy,
            'zero_crossing_rate': mean_zcr,
            'spectral_centroid': mean_spectral_centroid,
            'length_samples': len(audio)
        }
    
    @staticmethod
    def apply_bandpass_filter(audio: np.ndarray, sr: int, 
                            low_freq: int = 2000, high_freq: int = 12000) -> np.ndarray:
        """
        Band-pass filter uygula (ağustos böceği frekans aralığı için)
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            low_freq: Alt frekans sınırı
            high_freq: Üst frekans sınırı
            
        Returns:
            Filtrelenmiş audio sinyal
        """
        from scipy.signal import butter, filtfilt
        
        # Normalize frequencies
        nyquist = sr / 2
        low = low_freq / nyquist
        high = high_freq / nyquist
        
        # Butterworth band-pass filter
        b, a = butter(4, [low, high], btype='band')
        filtered_audio = filtfilt(b, a, audio)
        
        return filtered_audio
    
    @staticmethod
    def split_audio_windows(audio: np.ndarray, sr: int, 
                           window_size: int = 8, overlap: float = 0.5) -> list:
        """
        Audio sinyali pencere bazında böl (Dolbear yasası için 8 saniyelik pencereler)
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            window_size: Pencere boyutu (saniye)
            overlap: Örtüşme oranı (0-1)
            
        Returns:
            Audio pencerelerinin listesi
        """
        window_samples = int(window_size * sr)
        hop_samples = int(window_samples * (1 - overlap))
        
        windows = []
        start = 0
        
        while start + window_samples <= len(audio):
            window = audio[start:start + window_samples]
            windows.append(window)
            start += hop_samples
            
        return windows
    
    @staticmethod
    def save_audio(audio: np.ndarray, sr: int, output_path: str):
        """
        Audio dosyasını kaydet
        
        Args:
            audio: Audio sinyal
            sr: Sample rate
            output_path: Çıktı dosya yolu
        """
        try:
            sf.write(output_path, audio, sr)
        except Exception as e:
            raise ValueError(f"Audio dosyası kaydedilemedi / Could not save audio file: {str(e)}")
    
    @staticmethod
    def numpy_to_torch(audio: np.ndarray) -> torch.Tensor:
        """
        NumPy array'i PyTorch tensor'a dönüştür
        
        Args:
            audio: NumPy audio array
            
        Returns:
            PyTorch tensor
        """
        if isinstance(audio, torch.Tensor):
            return audio
        return torch.from_numpy(audio).float()
    
    @staticmethod
    def torch_to_numpy(audio: torch.Tensor) -> np.ndarray:
        """
        PyTorch tensor'ı NumPy array'e dönüştür
        
        Args:
            audio: PyTorch tensor
            
        Returns:
            NumPy array
        """
        if isinstance(audio, np.ndarray):
            return audio
        return audio.detach().cpu().numpy()
    
    @staticmethod
    def validate_audio_format(file_path: str) -> bool:
        """
        Audio dosya formatını doğrula
        
        Args:
            file_path: Dosya yolu
            
        Returns:
            Format geçerli mi?
        """
        supported_formats = ['.wav', '.mp3', '.m4a', '.flac', '.ogg']
        return any(file_path.lower().endswith(fmt) for fmt in supported_formats)
    
    @staticmethod
    def estimate_noise_level(audio: np.ndarray, method: str = 'quantile') -> float:
        """
        Audio sinyalindeki gürültü seviyesini tahmin et
        
        Args:
            audio: Audio sinyal
            method: Tahmin yöntemi ('quantile', 'rms')
            
        Returns:
            Gürültü seviyesi
        """
        if method == 'quantile':
            # Alt %10'luk dilim gürültü seviyesi olarak kabul edilir
            return np.percentile(np.abs(audio), 10)
        elif method == 'rms':
            # RMS tabanlı gürültü tahmini
            return np.sqrt(np.mean(audio**2)) * 0.1
        else:
            return 0.01  # Varsayılan değer
