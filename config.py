# 🦗 Ağustos Böceği Frekans Analizi Sistemi - Konfigürasyon
# Cicada Frequency Analysis System - Configuration

"""
Bu dosya sistem konfigürasyon ayarlarını içerir.
This file contains system configuration settings.

Author: Sems Kurtoglu
"""

import os

# Model konfigürasyonu / Model configuration
MODEL_CONFIG = {
    # Denoiser model seçenekleri / Denoiser model options
    'denoiser_model': 'dns48',  # dns48, dns64, master64
    'device': 'auto',           # cuda, cpu, auto
    'batch_size': 1,
    'precision': 'float32'
}

# Audio işleme konfigürasyonu / Audio processing configuration
AUDIO_CONFIG = {
    'target_sr': 22050,         # Hedef sample rate / Target sample rate
    'n_fft': 2048,              # FFT pencere boyutu / FFT window size
    'hop_length': 512,          # Hop length
    'window_size': 8,           # Dolbear yasası için pencere (saniye) / Window for Dolbear's law (seconds)
    'cicada_freq_range': (2000, 12000),  # Ağustos böceği frekans aralığı / Cicada frequency range
    'highpass_cutoff': 1000,    # Yüksek geçiren filtre kesim frekansı / High-pass filter cutoff
    'min_duration': 0.5,        # Minimum audio süresi (saniye) / Minimum audio duration (seconds)
    'max_duration': 300         # Maksimum audio süresi (saniye) / Maximum audio duration (seconds)
}

# Chirp tespit konfigürasyonu / Chirp detection configuration
CHIRP_CONFIG = {
    'min_chirp_duration': 0.05,     # Minimum chirp süresi (saniye) / Min chirp duration (seconds)
    'max_chirp_duration': 1.0,      # Maksimum chirp süresi (saniye) / Max chirp duration (seconds)
    'min_chirp_interval': 0.02,     # Minimum chirp arası süre / Min interval between chirps
    'energy_threshold': 0.1,        # Enerji eşiği / Energy threshold
    'prominence_threshold': 0.05,    # Peak prominence eşiği / Peak prominence threshold
    'adaptive_threshold': True,      # Adaptif eşik kullan / Use adaptive threshold
    'envelope_smoothing': 2.0        # Envelope smoothing sigma
}

# Sıcaklık hesaplama konfigürasyonu / Temperature calculation configuration
TEMPERATURE_CONFIG = {
    # Güven seviyesi eşikleri / Confidence level thresholds
    'confidence_thresholds': {
        'high': {'min_chirps': 20, 'max_std': 2.0, 'min_duration': 8},
        'medium': {'min_chirps': 10, 'max_std': 4.0, 'min_duration': 5},
        'low': {'min_chirps': 5, 'max_std': 6.0, 'min_duration': 3}
    },
    
    # Varsayılan hesaplama yöntemi / Default calculation method
    'default_method': 'basic_celsius',
    
    # Sıcaklık aralığı kontrolleri / Temperature range checks
    'temp_range_celsius': (-10, 50),   # Makul sıcaklık aralığı / Reasonable temperature range
    'temp_warning_threshold': 3.0       # Uyarı için std sapma eşiği / Std dev threshold for warning
}

# Gradio UI konfigürasyonu / Gradio UI configuration
UI_CONFIG = {
    'server_name': '0.0.0.0',      # Server IP (0.0.0.0 = tüm IP'ler / all IPs)
    'server_port': 7860,            # Server port
    'share': False,                 # Gradio public link oluştur / Create public link
    'debug': False,                 # Debug modu / Debug mode
    'show_error': True,             # Hata göster / Show errors
    'inbrowser': True,              # Tarayıcıda otomatik aç / Auto open in browser
    'max_file_size': 50 * 1024 * 1024,  # Maksimum dosya boyutu (50MB) / Max file size
    'default_language': 'tr'        # Varsayılan dil / Default language
}

# Loglama konfigürasyonu / Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',                # Log seviyesi / Log level
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'cicada_analyzer.log', # Log dosyası / Log file
    'max_size': 10 * 1024 * 1024,  # Maksimum log dosya boyutu / Max log file size
    'backup_count': 3               # Yedek dosya sayısı / Backup file count
}

# Performans konfigürasyonu / Performance configuration
PERFORMANCE_CONFIG = {
    'enable_multiprocessing': False,    # Çoklu işlem desteği / Multiprocessing support
    'max_workers': 2,                   # Maksimum worker sayısı / Max worker count
    'memory_limit_mb': 2048,            # Bellek sınırı (MB) / Memory limit (MB)
    'cache_enabled': True,              # Önbellek etkin / Cache enabled
    'cache_size': 100                   # Önbellek boyutu / Cache size
}

# Dosya yolları / File paths
PATHS = {
    'models_dir': 'models',             # Model dosyaları dizini / Models directory
    'cache_dir': 'cache',               # Önbellek dizini / Cache directory
    'logs_dir': 'logs',                 # Log dosyaları dizini / Logs directory
    'temp_dir': 'temp',                 # Geçici dosyalar dizini / Temporary files directory
    'output_dir': 'output'              # Çıktı dosyaları dizini / Output files directory
}

# Desteklenen dosya formatları / Supported file formats
SUPPORTED_FORMATS = {
    'audio': ['.wav', '.mp3', '.m4a', '.flac', '.ogg', '.aac'],
    'output': ['.wav', '.json', '.png', '.html']
}

# API konfigürasyonu (gelecek versiyonlar için) / API configuration (for future versions)
API_CONFIG = {
    'enable_api': False,            # REST API etkin / REST API enabled
    'api_port': 8000,               # API port
    'api_key_required': False,      # API key gerekli / API key required
    'rate_limit': 60,               # Dakikada maksimum istek / Max requests per minute
    'max_request_size': 10 * 1024 * 1024  # Maksimum istek boyutu / Max request size
}

# Geliştirici ayarları / Developer settings
DEV_CONFIG = {
    'debug_plots': False,           # Debug grafikleri göster / Show debug plots
    'save_intermediate': False,     # Ara sonuçları kaydet / Save intermediate results
    'profiling_enabled': False,     # Performans profiling / Performance profiling
    'verbose_logging': False        # Detaylı loglama / Verbose logging
}

def get_config(section: str = None) -> dict:
    """
    Konfigürasyon ayarlarını al
    Get configuration settings
    
    Args:
        section: Konfigürasyon bölümü / Configuration section
        
    Returns:
        Konfigürasyon dictionary / Configuration dictionary
    """
    configs = {
        'model': MODEL_CONFIG,
        'audio': AUDIO_CONFIG,
        'chirp': CHIRP_CONFIG,
        'temperature': TEMPERATURE_CONFIG,
        'ui': UI_CONFIG,
        'logging': LOGGING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'paths': PATHS,
        'formats': SUPPORTED_FORMATS,
        'api': API_CONFIG,
        'dev': DEV_CONFIG
    }
    
    if section:
        return configs.get(section, {})
    else:
        return configs

def create_directories():
    """
    Gerekli dizinleri oluştur
    Create necessary directories
    """
    for path in PATHS.values():
        if not os.path.exists(path):
            os.makedirs(path, exist_ok=True)
            print(f"📁 Dizin oluşturuldu / Directory created: {path}")

def validate_config() -> bool:
    """
    Konfigürasyon ayarlarını doğrula
    Validate configuration settings
    
    Returns:
        Konfigürasyon geçerli mi? / Is configuration valid?
    """
    try:
        # Temel kontroller / Basic checks
        assert AUDIO_CONFIG['target_sr'] > 0, "Sample rate must be positive"
        assert AUDIO_CONFIG['window_size'] > 0, "Window size must be positive"
        assert len(AUDIO_CONFIG['cicada_freq_range']) == 2, "Frequency range must have 2 values"
        assert AUDIO_CONFIG['cicada_freq_range'][0] < AUDIO_CONFIG['cicada_freq_range'][1], "Invalid frequency range"
        
        # Chirp konfigürasyon kontrolleri / Chirp configuration checks
        assert CHIRP_CONFIG['min_chirp_duration'] > 0, "Min chirp duration must be positive"
        assert CHIRP_CONFIG['max_chirp_duration'] > CHIRP_CONFIG['min_chirp_duration'], "Invalid chirp duration range"
        
        # UI konfigürasyon kontrolleri / UI configuration checks
        assert 1024 <= UI_CONFIG['server_port'] <= 65535, "Invalid server port"
        
        print("✅ Konfigürasyon doğrulandı / Configuration validated")
        return True
        
    except AssertionError as e:
        print(f"❌ Konfigürasyon hatası / Configuration error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Konfigürasyon doğrulama hatası / Configuration validation error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 Konfigürasyon kontrol ediliyor / Checking configuration...")
    
    # Dizinleri oluştur / Create directories
    create_directories()
    
    # Konfigürasyonu doğrula / Validate configuration
    if validate_config():
        print("🎉 Konfigürasyon hazır / Configuration ready")
    else:
        print("❌ Konfigürasyon sorunları var / Configuration has issues")
