"""
🦗 Basit Ağustos Böceği Analiz Arayüzü
Simple Cicada Analysis Interface

CDN ve i18n sorunlarını önlemek için basitleştirilmiş arayüz
"""

import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Cicada analyzer import
try:
    from cicada_analyzer.core.cicada_filter import CicadaFilter
    cicada_filter = None
    
    def initialize_system():
        global cicada_filter
        if cicada_filter is None:
            print("🦗 Sistem başlatılıyor...")
            cicada_filter = CicadaFilter()
            print("✅ Sistem hazır!")
        return cicada_filter
    
except ImportError as e:
    print(f"❌ Import hatası: {e}")
    cicada_filter = None

def analyze_audio_simple(audio_file, enable_denoising, enable_chirp, temp_method):
    """Basit audio analiz fonksiyonu"""
    
    if audio_file is None:
        return "❌ Lütfen bir audio dosyası yükleyin.", None
    
    try:
        # Sistem başlat
        filter_obj = initialize_system()
        if filter_obj is None:
            return "❌ Sistem başlatılamadı.", None
        
        # Analiz yap
        result = filter_obj.process_audio_file(
            file_path=audio_file,
            enable_denoising=enable_denoising,
            enable_chirp_detection=enable_chirp,
            temperature_method=temp_method
        )
        
        if result['success']:
            temp_info = result['temperature_info']
            temp_text = f"""
## 🌡️ Sıcaklık Tahmini

**Tahmin Edilen Sıcaklık:** {temp_info['estimated_temperature']:.1f}°C

**Chirp Bilgileri:**
- Tespit edilen chirp sayısı: {temp_info['chirp_count']}
- Ortalama chirp frekansı: {temp_info['avg_chirp_frequency']:.2f} Hz
- Güven seviyesi: {temp_info['confidence_level']:.1f}%

**Kullanılan Yöntem:** {temp_method}
"""
            
            # Basit görselleştirme
            fig, ax = plt.subplots(figsize=(10, 6))
            ax.text(0.5, 0.5, f"Sıcaklık: {temp_info['estimated_temperature']:.1f}°C", 
                   ha='center', va='center', fontsize=24, 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            ax.set_title("🦗 Ağustos Böceği Sıcaklık Analizi", fontsize=16)
            
            return temp_text, fig
        else:
            return f"❌ Analiz hatası: {result.get('error', 'Bilinmeyen hata')}", None
            
    except Exception as e:
        return f"❌ Sistem hatası: {str(e)}", None

# Basit Gradio arayüzü
def create_simple_interface():
    with gr.Blocks(
        title="🦗 Cicada Analysis - Simple",
        css=".gradio-container { max-width: 800px; margin: auto; }",
        analytics_enabled=False
    ) as app:
        
        gr.Markdown("""
        # 🦗 Ağustos Böceği Sıcaklık Analizi
        ## Dolbear Yasası ile Sıcaklık Tahmini
        
        Bu basit arayüz, ağustos böceği seslerinden sıcaklık tahmin eder.
        """)
        
        with gr.Row():
            with gr.Column():
                # Girdi kontrolleri
                audio_input = gr.Audio(
                    label="🎵 Audio Dosyası Yükleyin",
                    type="filepath",
                    sources=["upload"]
                )
                
                with gr.Row():
                    enable_denoising = gr.Checkbox(
                        label="🧹 Ses Temizleme", 
                        value=True
                    )
                    enable_chirp = gr.Checkbox(
                        label="🔍 Chirp Tespiti", 
                        value=True
                    )
                
                temp_method = gr.Dropdown(
                    choices=[
                        ("Temel Dolbear (Celsius)", "basic_celsius"),
                        ("Cicada orni Özel", "cicada_orni_special"),
                        ("Dolbear (Fahrenheit)", "dolbear_fahrenheit")
                    ],
                    value="basic_celsius",
                    label="🌡️ Sıcaklık Hesaplama Yöntemi"
                )
                
                analyze_btn = gr.Button("🚀 Analiz Et", variant="primary")
            
            with gr.Column():
                # Çıktı alanları
                result_text = gr.Markdown(label="📊 Sonuçlar")
                result_plot = gr.Plot(label="📈 Görselleştirme")
        
        # Event handler
        analyze_btn.click(
            analyze_audio_simple,
            inputs=[audio_input, enable_denoising, enable_chirp, temp_method],
            outputs=[result_text, result_plot]
        )
        
        gr.Markdown("""
        ---
        ### 📚 Dolbear Yasası Hakkında
        
        **Amos Dolbear (1897)** tarafından keşfedilen bu yasa:
        - T(°C) = N₈ + 5 (8 saniyedeki chirp sayısı + 5)
        - Ağustos böcekleri soğukkanlı olduğu için metabolizmaları sıcaklığa bağlıdır
        - Yüksek sıcaklıklarda daha hızlı chirp yaparlar
        """)
    
    return app

if __name__ == "__main__":
    print("🦗 Basit Cicada Analysis başlatılıyor...")
    
    app = create_simple_interface()
    
    app.launch(
        server_name="127.0.0.1",
        server_port=7862,
        share=False,
        debug=False,
        show_error=True,
        inbrowser=False,
        quiet=False
    )
